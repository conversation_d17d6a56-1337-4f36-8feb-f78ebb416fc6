{"version": 3, "sources": ["../../svelte/src/internal/client/dev/assign.js", "../../svelte/src/internal/client/dev/css.js", "../../svelte/src/internal/client/dev/elements.js", "../../svelte/src/internal/client/dev/hmr.js", "../../svelte/src/internal/client/dev/ownership.js", "../../svelte/src/internal/client/dev/legacy.js", "../../svelte/src/internal/client/dev/inspect.js", "../../svelte/src/internal/client/dev/validation.js", "../../svelte/src/internal/client/dom/blocks/await.js", "../../svelte/src/internal/client/dom/blocks/if.js", "../../svelte/src/internal/client/dom/blocks/key.js", "../../svelte/src/internal/client/dom/blocks/css-props.js", "../../svelte/src/internal/client/dom/blocks/each.js", "../../svelte/src/internal/client/dom/blocks/html.js", "../../svelte/src/internal/client/dom/blocks/slot.js", "../../svelte/src/internal/shared/validate.js", "../../svelte/src/internal/client/dom/blocks/snippet.js", "../../svelte/src/internal/client/dom/blocks/svelte-component.js", "../../svelte/src/internal/client/dom/blocks/svelte-element.js", "../../svelte/src/internal/client/dom/css.js", "../../svelte/src/internal/client/dom/elements/actions.js", "../../svelte/src/internal/client/dom/elements/attachments.js", "../../svelte/src/escaping.js", "../../svelte/src/internal/shared/attributes.js", "../../svelte/src/internal/client/dom/elements/class.js", "../../svelte/src/internal/client/dom/elements/style.js", "../../svelte/src/internal/client/dom/elements/attributes.js", "../../svelte/src/internal/client/timing.js", "../../svelte/src/internal/client/loop.js", "../../svelte/src/internal/client/dom/elements/transitions.js", "../../svelte/src/internal/client/dom/elements/bindings/document.js", "../../svelte/src/internal/client/dom/elements/bindings/input.js", "../../svelte/src/internal/client/dom/elements/bindings/media.js", "../../svelte/src/internal/client/dom/elements/bindings/navigator.js", "../../svelte/src/internal/client/dom/elements/bindings/props.js", "../../svelte/src/internal/client/dom/elements/bindings/select.js", "../../svelte/src/internal/client/dom/elements/bindings/size.js", "../../svelte/src/internal/client/dom/elements/bindings/this.js", "../../svelte/src/internal/client/dom/elements/bindings/universal.js", "../../svelte/src/internal/client/dom/elements/bindings/window.js", "../../svelte/src/internal/client/dom/legacy/lifecycle.js", "../../svelte/src/internal/client/dom/legacy/misc.js", "../../svelte/src/index-client.js", "../../svelte/src/store/utils.js", "../../svelte/src/store/shared/index.js", "../../svelte/src/internal/client/reactivity/store.js", "../../svelte/src/internal/client/reactivity/props.js", "../../svelte/src/internal/client/dom/blocks/boundary.js", "../../svelte/src/internal/client/validate.js", "../../svelte/src/internal/client/dom/elements/custom-element.js", "../../svelte/src/internal/client/dev/console-log.js", "../../svelte/src/attachments/index.js"], "sourcesContent": ["import { sanitize_location } from '../../../utils.js';\nimport { untrack } from '../runtime.js';\nimport * as w from '../warnings.js';\n\n/**\n *\n * @param {any} a\n * @param {any} b\n * @param {string} property\n * @param {string} location\n */\nfunction compare(a, b, property, location) {\n\tif (a !== b) {\n\t\tw.assignment_value_stale(property, /** @type {string} */ (sanitize_location(location)));\n\t}\n\n\treturn a;\n}\n\n/**\n * @param {any} object\n * @param {string} property\n * @param {any} value\n * @param {string} location\n */\nexport function assign(object, property, value, location) {\n\treturn compare(\n\t\t(object[property] = value),\n\t\tuntrack(() => object[property]),\n\t\tproperty,\n\t\tlocation\n\t);\n}\n\n/**\n * @param {any} object\n * @param {string} property\n * @param {any} value\n * @param {string} location\n */\nexport function assign_and(object, property, value, location) {\n\treturn compare(\n\t\t(object[property] &&= value),\n\t\tuntrack(() => object[property]),\n\t\tproperty,\n\t\tlocation\n\t);\n}\n\n/**\n * @param {any} object\n * @param {string} property\n * @param {any} value\n * @param {string} location\n */\nexport function assign_or(object, property, value, location) {\n\treturn compare(\n\t\t(object[property] ||= value),\n\t\tuntrack(() => object[property]),\n\t\tproperty,\n\t\tlocation\n\t);\n}\n\n/**\n * @param {any} object\n * @param {string} property\n * @param {any} value\n * @param {string} location\n */\nexport function assign_nullish(object, property, value, location) {\n\treturn compare(\n\t\t(object[property] ??= value),\n\t\tuntrack(() => object[property]),\n\t\tproperty,\n\t\tlocation\n\t);\n}\n", "/** @type {Map<String, Set<HTMLStyleElement>>} */\nvar all_styles = new Map();\n\n/**\n * @param {String} hash\n * @param {HTMLStyleElement} style\n */\nexport function register_style(hash, style) {\n\tvar styles = all_styles.get(hash);\n\n\tif (!styles) {\n\t\tstyles = new Set();\n\t\tall_styles.set(hash, styles);\n\t}\n\n\tstyles.add(style);\n}\n\n/**\n * @param {String} hash\n */\nexport function cleanup_styles(hash) {\n\tvar styles = all_styles.get(hash);\n\tif (!styles) return;\n\n\tfor (const style of styles) {\n\t\tstyle.remove();\n\t}\n\n\tall_styles.delete(hash);\n}\n", "/** @import { SourceLocation } from '#client' */\nimport { HY<PERSON><PERSON>ION_END, HYDRATION_START, HYDRATION_START_ELSE } from '../../../constants.js';\nimport { hydrating } from '../dom/hydration.js';\n\n/**\n * @param {any} fn\n * @param {string} filename\n * @param {SourceLocation[]} locations\n * @returns {any}\n */\nexport function add_locations(fn, filename, locations) {\n\treturn (/** @type {any[]} */ ...args) => {\n\t\tconst dom = fn(...args);\n\n\t\tvar node = hydrating ? dom : dom.nodeType === 11 ? dom.firstChild : dom;\n\t\tassign_locations(node, filename, locations);\n\n\t\treturn dom;\n\t};\n}\n\n/**\n * @param {Element} element\n * @param {string} filename\n * @param {SourceLocation} location\n */\nfunction assign_location(element, filename, location) {\n\t// @ts-expect-error\n\telement.__svelte_meta = {\n\t\tloc: { file: filename, line: location[0], column: location[1] }\n\t};\n\n\tif (location[2]) {\n\t\tassign_locations(element.firstChild, filename, location[2]);\n\t}\n}\n\n/**\n * @param {Node | null} node\n * @param {string} filename\n * @param {SourceLocation[]} locations\n */\nfunction assign_locations(node, filename, locations) {\n\tvar i = 0;\n\tvar depth = 0;\n\n\twhile (node && i < locations.length) {\n\t\tif (hydrating && node.nodeType === 8) {\n\t\t\tvar comment = /** @type {Comment} */ (node);\n\t\t\tif (comment.data === HYDRATION_START || comment.data === HYDRATION_START_ELSE) depth += 1;\n\t\t\telse if (comment.data[0] === HYDRATION_END) depth -= 1;\n\t\t}\n\n\t\tif (depth === 0 && node.nodeType === 1) {\n\t\t\tassign_location(/** @type {Element} */ (node), filename, locations[i++]);\n\t\t}\n\n\t\tnode = node.nextSibling;\n\t}\n}\n", "/** @import { Source, Effect, TemplateNode } from '#client' */\nimport { FILENAME, HMR } from '../../../constants.js';\nimport { EFFECT_TRANSPARENT } from '#client/constants';\nimport { hydrate_node, hydrating } from '../dom/hydration.js';\nimport { block, branch, destroy_effect } from '../reactivity/effects.js';\nimport { source } from '../reactivity/sources.js';\nimport { set_should_intro } from '../render.js';\nimport { get } from '../runtime.js';\n\n/**\n * @template {(anchor: Comment, props: any) => any} Component\n * @param {Component} original\n * @param {() => Source<Component>} get_source\n */\nexport function hmr(original, get_source) {\n\t/**\n\t * @param {TemplateNode} anchor\n\t * @param {any} props\n\t */\n\tfunction wrapper(anchor, props) {\n\t\tlet instance = {};\n\n\t\t/** @type {Effect} */\n\t\tlet effect;\n\n\t\tlet ran = false;\n\n\t\tblock(() => {\n\t\t\tconst source = get_source();\n\t\t\tconst component = get(source);\n\n\t\t\tif (effect) {\n\t\t\t\t// @ts-ignore\n\t\t\t\tfor (var k in instance) delete instance[k];\n\t\t\t\tdestroy_effect(effect);\n\t\t\t}\n\n\t\t\teffect = branch(() => {\n\t\t\t\t// when the component is invalidated, replace it without transitions\n\t\t\t\tif (ran) set_should_intro(false);\n\n\t\t\t\t// preserve getters/setters\n\t\t\t\tObject.defineProperties(\n\t\t\t\t\tinstance,\n\t\t\t\t\tObject.getOwnPropertyDescriptors(\n\t\t\t\t\t\t// @ts-expect-error\n\t\t\t\t\t\tnew.target ? new component(anchor, props) : component(anchor, props)\n\t\t\t\t\t)\n\t\t\t\t);\n\n\t\t\t\tif (ran) set_should_intro(true);\n\t\t\t});\n\t\t}, EFFECT_TRANSPARENT);\n\n\t\tran = true;\n\n\t\tif (hydrating) {\n\t\t\tanchor = hydrate_node;\n\t\t}\n\n\t\treturn instance;\n\t}\n\n\t// @ts-expect-error\n\twrapper[FILENAME] = original[FILENAME];\n\n\t// @ts-expect-error\n\twrapper[HMR] = {\n\t\t// When we accept an update, we set the original source to the new component\n\t\toriginal,\n\t\t// The `get_source` parameter reads `wrapper[HMR].source`, but in the `accept`\n\t\t// function we always replace it with `previous[HMR].source`, which in practice\n\t\t// means we only ever update the original\n\t\tsource: source(original)\n\t};\n\n\treturn wrapper;\n}\n", "/** @typedef {{ file: string, line: number, column: number }} Location */\n\nimport { get_descriptor } from '../../shared/utils.js';\nimport { LEGACY_PROPS, STATE_SYMBOL } from '#client/constants';\nimport { FILENAME } from '../../../constants.js';\nimport { component_context } from '../context.js';\nimport * as w from '../warnings.js';\nimport { sanitize_location } from '../../../utils.js';\n\n/**\n * Sets up a validator that\n * - traverses the path of a prop to find out if it is allowed to be mutated\n * - checks that the binding chain is not interrupted\n * @param {Record<string, any>} props\n */\nexport function create_ownership_validator(props) {\n\tconst component = component_context?.function;\n\tconst parent = component_context?.p?.function;\n\n\treturn {\n\t\t/**\n\t\t * @param {string} prop\n\t\t * @param {any[]} path\n\t\t * @param {any} result\n\t\t * @param {number} line\n\t\t * @param {number} column\n\t\t */\n\t\tmutation: (prop, path, result, line, column) => {\n\t\t\tconst name = path[0];\n\t\t\tif (is_bound_or_unset(props, name) || !parent) {\n\t\t\t\treturn result;\n\t\t\t}\n\n\t\t\t/** @type {any} */\n\t\t\tlet value = props;\n\n\t\t\tfor (let i = 0; i < path.length - 1; i++) {\n\t\t\t\tvalue = value[path[i]];\n\t\t\t\tif (!value?.[STATE_SYMBOL]) {\n\t\t\t\t\treturn result;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tconst location = sanitize_location(`${component[FILENAME]}:${line}:${column}`);\n\n\t\t\tw.ownership_invalid_mutation(name, location, prop, parent[FILENAME]);\n\n\t\t\treturn result;\n\t\t},\n\t\t/**\n\t\t * @param {any} key\n\t\t * @param {any} child_component\n\t\t * @param {() => any} value\n\t\t */\n\t\tbinding: (key, child_component, value) => {\n\t\t\tif (!is_bound_or_unset(props, key) && parent && value()?.[STATE_SYMBOL]) {\n\t\t\t\tw.ownership_invalid_binding(\n\t\t\t\t\tcomponent[FILENAME],\n\t\t\t\t\tkey,\n\t\t\t\t\tchild_component[FILENAME],\n\t\t\t\t\tparent[FILENAME]\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\t};\n}\n\n/**\n * @param {Record<string, any>} props\n * @param {string} prop_name\n */\nfunction is_bound_or_unset(props, prop_name) {\n\t// Can be the case when someone does `mount(Component, props)` with `let props = $state({...})`\n\t// or `createClassComponent(Component, props)`\n\tconst is_entry_props = STATE_SYMBOL in props || LEGACY_PROPS in props;\n\treturn (\n\t\t!!get_descriptor(props, prop_name)?.set ||\n\t\t(is_entry_props && prop_name in props) ||\n\t\t!(prop_name in props)\n\t);\n}\n", "import * as e from '../errors.js';\nimport { component_context } from '../context.js';\nimport { FILENAME } from '../../../constants.js';\n\n/** @param {Function & { [FILENAME]: string }} target */\nexport function check_target(target) {\n\tif (target) {\n\t\te.component_api_invalid_new(target[FILENAME] ?? 'a component', target.name);\n\t}\n}\n\nexport function legacy_api() {\n\tconst component = component_context?.function;\n\n\t/** @param {string} method */\n\tfunction error(method) {\n\t\te.component_api_changed(method, component[FILENAME]);\n\t}\n\n\treturn {\n\t\t$destroy: () => error('$destroy()'),\n\t\t$on: () => error('$on(...)'),\n\t\t$set: () => error('$set(...)')\n\t};\n}\n", "import { UNINITIALIZED } from '../../../constants.js';\nimport { snapshot } from '../../shared/clone.js';\nimport { inspect_effect, validate_effect } from '../reactivity/effects.js';\n\n/**\n * @param {() => any[]} get_value\n * @param {Function} [inspector]\n */\n// eslint-disable-next-line no-console\nexport function inspect(get_value, inspector = console.log) {\n\tvalidate_effect('$inspect');\n\n\tlet initial = true;\n\n\tinspect_effect(() => {\n\t\t/** @type {any} */\n\t\tvar value = UNINITIALIZED;\n\n\t\t// Capturing the value might result in an exception due to the inspect effect being\n\t\t// sync and thus operating on stale data. In the case we encounter an exception we\n\t\t// can bail-out of reporting the value. Instead we simply console.error the error\n\t\t// so at least it's known that an error occured, but we don't stop execution\n\t\ttry {\n\t\t\tvalue = get_value();\n\t\t} catch (error) {\n\t\t\t// eslint-disable-next-line no-console\n\t\t\tconsole.error(error);\n\t\t}\n\n\t\tif (value !== UNINITIALIZED) {\n\t\t\tinspector(initial ? 'init' : 'update', ...snapshot(value, true));\n\t\t}\n\n\t\tinitial = false;\n\t});\n}\n", "import { invalid_snippet_arguments } from '../../shared/errors.js';\n/**\n * @param {Node} anchor\n * @param {...(()=>any)[]} args\n */\nexport function validate_snippet_args(anchor, ...args) {\n\tif (typeof anchor !== 'object' || !(anchor instanceof Node)) {\n\t\tinvalid_snippet_arguments();\n\t}\n\tfor (let arg of args) {\n\t\tif (typeof arg !== 'function') {\n\t\t\tinvalid_snippet_arguments();\n\t\t}\n\t}\n}\n", "/** @import { Effect, Source, TemplateNode } from '#client' */\nimport { DEV } from 'esm-env';\nimport { is_promise } from '../../../shared/utils.js';\nimport { block, branch, pause_effect, resume_effect } from '../../reactivity/effects.js';\nimport { internal_set, mutable_source, source } from '../../reactivity/sources.js';\nimport { flushSync, set_active_effect, set_active_reaction } from '../../runtime.js';\nimport {\n\thydrate_next,\n\thydrate_node,\n\thydrating,\n\tremove_nodes,\n\tset_hydrate_node,\n\tset_hydrating\n} from '../hydration.js';\nimport { queue_micro_task } from '../task.js';\nimport { HYDRATION_START_ELSE, UNINITIALIZED } from '../../../../constants.js';\nimport {\n\tcomponent_context,\n\tis_runes,\n\tset_component_context,\n\tset_dev_current_component_function\n} from '../../context.js';\n\nconst PENDING = 0;\nconst THEN = 1;\nconst CATCH = 2;\n\n/**\n * @template V\n * @param {TemplateNode} node\n * @param {(() => Promise<V>)} get_input\n * @param {null | ((anchor: Node) => void)} pending_fn\n * @param {null | ((anchor: Node, value: Source<V>) => void)} then_fn\n * @param {null | ((anchor: Node, error: unknown) => void)} catch_fn\n * @returns {void}\n */\nexport function await_block(node, get_input, pending_fn, then_fn, catch_fn) {\n\tif (hydrating) {\n\t\thydrate_next();\n\t}\n\n\tvar anchor = node;\n\tvar runes = is_runes();\n\tvar active_component_context = component_context;\n\n\t/** @type {any} */\n\tvar component_function = DEV ? component_context?.function : null;\n\n\t/** @type {V | Promise<V> | typeof UNINITIALIZED} */\n\tvar input = UNINITIALIZED;\n\n\t/** @type {Effect | null} */\n\tvar pending_effect;\n\n\t/** @type {Effect | null} */\n\tvar then_effect;\n\n\t/** @type {Effect | null} */\n\tvar catch_effect;\n\n\tvar input_source = (runes ? source : mutable_source)(/** @type {V} */ (undefined));\n\tvar error_source = (runes ? source : mutable_source)(undefined);\n\tvar resolved = false;\n\n\t/**\n\t * @param {PENDING | THEN | CATCH} state\n\t * @param {boolean} restore\n\t */\n\tfunction update(state, restore) {\n\t\tresolved = true;\n\n\t\tif (restore) {\n\t\t\tset_active_effect(effect);\n\t\t\tset_active_reaction(effect); // TODO do we need both?\n\t\t\tset_component_context(active_component_context);\n\t\t\tif (DEV) set_dev_current_component_function(component_function);\n\t\t}\n\n\t\ttry {\n\t\t\tif (state === PENDING && pending_fn) {\n\t\t\t\tif (pending_effect) resume_effect(pending_effect);\n\t\t\t\telse pending_effect = branch(() => pending_fn(anchor));\n\t\t\t}\n\n\t\t\tif (state === THEN && then_fn) {\n\t\t\t\tif (then_effect) resume_effect(then_effect);\n\t\t\t\telse then_effect = branch(() => then_fn(anchor, input_source));\n\t\t\t}\n\n\t\t\tif (state === CATCH && catch_fn) {\n\t\t\t\tif (catch_effect) resume_effect(catch_effect);\n\t\t\t\telse catch_effect = branch(() => catch_fn(anchor, error_source));\n\t\t\t}\n\n\t\t\tif (state !== PENDING && pending_effect) {\n\t\t\t\tpause_effect(pending_effect, () => (pending_effect = null));\n\t\t\t}\n\n\t\t\tif (state !== THEN && then_effect) {\n\t\t\t\tpause_effect(then_effect, () => (then_effect = null));\n\t\t\t}\n\n\t\t\tif (state !== CATCH && catch_effect) {\n\t\t\t\tpause_effect(catch_effect, () => (catch_effect = null));\n\t\t\t}\n\t\t} finally {\n\t\t\tif (restore) {\n\t\t\t\tif (DEV) set_dev_current_component_function(null);\n\t\t\t\tset_component_context(null);\n\t\t\t\tset_active_reaction(null);\n\t\t\t\tset_active_effect(null);\n\n\t\t\t\t// without this, the DOM does not update until two ticks after the promise\n\t\t\t\t// resolves, which is unexpected behaviour (and somewhat irksome to test)\n\t\t\t\tflushSync();\n\t\t\t}\n\t\t}\n\t}\n\n\tvar effect = block(() => {\n\t\tif (input === (input = get_input())) return;\n\n\t\t/** Whether or not there was a hydration mismatch. Needs to be a `let` or else it isn't treeshaken out */\n\t\t// @ts-ignore coercing `anchor` to a `Comment` causes TypeScript and Prettier to fight\n\t\tlet mismatch = hydrating && is_promise(input) === (anchor.data === HYDRATION_START_ELSE);\n\n\t\tif (mismatch) {\n\t\t\t// Hydration mismatch: remove everything inside the anchor and start fresh\n\t\t\tanchor = remove_nodes();\n\n\t\t\tset_hydrate_node(anchor);\n\t\t\tset_hydrating(false);\n\t\t\tmismatch = true;\n\t\t}\n\n\t\tif (is_promise(input)) {\n\t\t\tvar promise = input;\n\n\t\t\tresolved = false;\n\n\t\t\tpromise.then(\n\t\t\t\t(value) => {\n\t\t\t\t\tif (promise !== input) return;\n\t\t\t\t\t// we technically could use `set` here since it's on the next microtick\n\t\t\t\t\t// but let's use internal_set for consistency and just to be safe\n\t\t\t\t\tinternal_set(input_source, value);\n\t\t\t\t\tupdate(THEN, true);\n\t\t\t\t},\n\t\t\t\t(error) => {\n\t\t\t\t\tif (promise !== input) return;\n\t\t\t\t\t// we technically could use `set` here since it's on the next microtick\n\t\t\t\t\t// but let's use internal_set for consistency and just to be safe\n\t\t\t\t\tinternal_set(error_source, error);\n\t\t\t\t\tupdate(CATCH, true);\n\t\t\t\t\tif (!catch_fn) {\n\t\t\t\t\t\t// Rethrow the error if no catch block exists\n\t\t\t\t\t\tthrow error_source.v;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t);\n\n\t\t\tif (hydrating) {\n\t\t\t\tif (pending_fn) {\n\t\t\t\t\tpending_effect = branch(() => pending_fn(anchor));\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// Wait a microtask before checking if we should show the pending state as\n\t\t\t\t// the promise might have resolved by the next microtask.\n\t\t\t\tqueue_micro_task(() => {\n\t\t\t\t\tif (!resolved) update(PENDING, true);\n\t\t\t\t});\n\t\t\t}\n\t\t} else {\n\t\t\tinternal_set(input_source, input);\n\t\t\tupdate(THEN, false);\n\t\t}\n\n\t\tif (mismatch) {\n\t\t\t// continue in hydration mode\n\t\t\tset_hydrating(true);\n\t\t}\n\n\t\t// Set the input to something else, in order to disable the promise callbacks\n\t\treturn () => (input = UNINITIALIZED);\n\t});\n\n\tif (hydrating) {\n\t\tanchor = hydrate_node;\n\t}\n}\n", "/** @import { Effect, TemplateNode } from '#client' */\nimport { EFFECT_TRANSPARENT } from '#client/constants';\nimport {\n\thydrate_next,\n\thydrate_node,\n\thydrating,\n\tread_hydration_instruction,\n\tremove_nodes,\n\tset_hydrate_node,\n\tset_hydrating\n} from '../hydration.js';\nimport { block, branch, pause_effect, resume_effect } from '../../reactivity/effects.js';\nimport { HYDRATION_START, HYDRATION_START_ELSE, UNINITIALIZED } from '../../../../constants.js';\n\n/**\n * @param {TemplateNode} node\n * @param {(branch: (fn: (anchor: Node, elseif?: [number,number]) => void, flag?: boolean) => void) => void} fn\n * @param {[number,number]} [elseif]\n * @returns {void}\n */\nexport function if_block(node, fn, [root_index, hydrate_index] = [0, 0]) {\n\tif (hydrating && root_index === 0) {\n\t\thydrate_next();\n\t}\n\n\tvar anchor = node;\n\n\t/** @type {Effect | null} */\n\tvar consequent_effect = null;\n\n\t/** @type {Effect | null} */\n\tvar alternate_effect = null;\n\n\t/** @type {UNINITIALIZED | boolean | null} */\n\tvar condition = UNINITIALIZED;\n\n\tvar flags = root_index > 0 ? EFFECT_TRANSPARENT : 0;\n\n\tvar has_branch = false;\n\n\tconst set_branch = (\n\t\t/** @type {(anchor: Node, elseif?: [number,number]) => void} */ fn,\n\t\tflag = true\n\t) => {\n\t\thas_branch = true;\n\t\tupdate_branch(flag, fn);\n\t};\n\n\tconst update_branch = (\n\t\t/** @type {boolean | null} */ new_condition,\n\t\t/** @type {null | ((anchor: Node, elseif?: [number,number]) => void)} */ fn\n\t) => {\n\t\tif (condition === (condition = new_condition)) return;\n\n\t\t/** Whether or not there was a hydration mismatch. Needs to be a `let` or else it isn't treeshaken out */\n\t\tlet mismatch = false;\n\n\t\tif (hydrating && hydrate_index !== -1) {\n\t\t\tif (root_index === 0) {\n\t\t\t\tconst data = read_hydration_instruction(anchor);\n\n\t\t\t\tif (data === HYDRATION_START) {\n\t\t\t\t\thydrate_index = 0;\n\t\t\t\t} else if (data === HYDRATION_START_ELSE) {\n\t\t\t\t\thydrate_index = Infinity;\n\t\t\t\t} else {\n\t\t\t\t\thydrate_index = parseInt(data.substring(1));\n\t\t\t\t\tif (hydrate_index !== hydrate_index) {\n\t\t\t\t\t\t// if hydrate_index is NaN\n\t\t\t\t\t\t// we set an invalid index to force mismatch\n\t\t\t\t\t\thydrate_index = condition ? Infinity : -1;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tconst is_else = hydrate_index > root_index;\n\n\t\t\tif (!!condition === is_else) {\n\t\t\t\t// Hydration mismatch: remove everything inside the anchor and start fresh.\n\t\t\t\t// This could happen with `{#if browser}...{/if}`, for example\n\t\t\t\tanchor = remove_nodes();\n\n\t\t\t\tset_hydrate_node(anchor);\n\t\t\t\tset_hydrating(false);\n\t\t\t\tmismatch = true;\n\t\t\t\thydrate_index = -1; // ignore hydration in next else if\n\t\t\t}\n\t\t}\n\n\t\tif (condition) {\n\t\t\tif (consequent_effect) {\n\t\t\t\tresume_effect(consequent_effect);\n\t\t\t} else if (fn) {\n\t\t\t\tconsequent_effect = branch(() => fn(anchor));\n\t\t\t}\n\n\t\t\tif (alternate_effect) {\n\t\t\t\tpause_effect(alternate_effect, () => {\n\t\t\t\t\talternate_effect = null;\n\t\t\t\t});\n\t\t\t}\n\t\t} else {\n\t\t\tif (alternate_effect) {\n\t\t\t\tresume_effect(alternate_effect);\n\t\t\t} else if (fn) {\n\t\t\t\talternate_effect = branch(() => fn(anchor, [root_index + 1, hydrate_index]));\n\t\t\t}\n\n\t\t\tif (consequent_effect) {\n\t\t\t\tpause_effect(consequent_effect, () => {\n\t\t\t\t\tconsequent_effect = null;\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tif (mismatch) {\n\t\t\t// continue in hydration mode\n\t\t\tset_hydrating(true);\n\t\t}\n\t};\n\n\tblock(() => {\n\t\thas_branch = false;\n\t\tfn(set_branch);\n\t\tif (!has_branch) {\n\t\t\tupdate_branch(null, null);\n\t\t}\n\t}, flags);\n\n\tif (hydrating) {\n\t\tanchor = hydrate_node;\n\t}\n}\n", "/** @import { Effect, TemplateNode } from '#client' */\nimport { UNINITIALIZED } from '../../../../constants.js';\nimport { block, branch, pause_effect } from '../../reactivity/effects.js';\nimport { not_equal, safe_not_equal } from '../../reactivity/equality.js';\nimport { is_runes } from '../../context.js';\nimport { hydrate_next, hydrate_node, hydrating } from '../hydration.js';\n\n/**\n * @template V\n * @param {TemplateNode} node\n * @param {() => V} get_key\n * @param {(anchor: Node) => TemplateNode | void} render_fn\n * @returns {void}\n */\nexport function key_block(node, get_key, render_fn) {\n\tif (hydrating) {\n\t\thydrate_next();\n\t}\n\n\tvar anchor = node;\n\n\t/** @type {V | typeof UNINITIALIZED} */\n\tvar key = UNINITIALIZED;\n\n\t/** @type {Effect} */\n\tvar effect;\n\n\tvar changed = is_runes() ? not_equal : safe_not_equal;\n\n\tblock(() => {\n\t\tif (changed(key, (key = get_key()))) {\n\t\t\tif (effect) {\n\t\t\t\tpause_effect(effect);\n\t\t\t}\n\n\t\t\teffect = branch(() => render_fn(anchor));\n\t\t}\n\t});\n\n\tif (hydrating) {\n\t\tanchor = hydrate_node;\n\t}\n}\n", "/** @import { TemplateNode } from '#client' */\nimport { render_effect, teardown } from '../../reactivity/effects.js';\nimport { hydrating, set_hydrate_node } from '../hydration.js';\nimport { get_first_child } from '../operations.js';\n\n/**\n * @param {HTMLDivElement | SVGGElement} element\n * @param {() => Record<string, string>} get_styles\n * @returns {void}\n */\nexport function css_props(element, get_styles) {\n\tif (hydrating) {\n\t\tset_hydrate_node(/** @type {TemplateNode} */ (get_first_child(element)));\n\t}\n\n\trender_effect(() => {\n\t\tvar styles = get_styles();\n\n\t\tfor (var key in styles) {\n\t\t\tvar value = styles[key];\n\n\t\t\tif (value) {\n\t\t\t\telement.style.setProperty(key, value);\n\t\t\t} else {\n\t\t\t\telement.style.removeProperty(key);\n\t\t\t}\n\t\t}\n\t});\n\n\tteardown(() => {\n\t\telement.remove();\n\t});\n}\n", "/** @import { EachItem, EachState, Effect, MaybeSource, Source, TemplateNode, TransitionManager, Value } from '#client' */\nimport {\n\tEACH_INDEX_REACTIVE,\n\tEACH_IS_ANIMATED,\n\tEACH_IS_CONTROLLED,\n\tEACH_ITEM_IMMUTABLE,\n\tEACH_ITEM_REACTIVE,\n\tHY<PERSON>ATION_END,\n\tHYDRATION_START_ELSE\n} from '../../../../constants.js';\nimport {\n\thydrate_next,\n\thydrate_node,\n\thydrating,\n\tread_hydration_instruction,\n\tremove_nodes,\n\tset_hydrate_node,\n\tset_hydrating\n} from '../hydration.js';\nimport {\n\tclear_text_content,\n\tcreate_text,\n\tget_first_child,\n\tget_next_sibling\n} from '../operations.js';\nimport {\n\tblock,\n\tbranch,\n\tdestroy_effect,\n\trun_out_transitions,\n\tpause_children,\n\tpause_effect,\n\tresume_effect\n} from '../../reactivity/effects.js';\nimport { source, mutable_source, internal_set } from '../../reactivity/sources.js';\nimport { array_from, is_array } from '../../../shared/utils.js';\nimport { INERT } from '#client/constants';\nimport { queue_micro_task } from '../task.js';\nimport { active_effect, get } from '../../runtime.js';\nimport { DEV } from 'esm-env';\nimport { derived_safe_equal } from '../../reactivity/deriveds.js';\n\n/**\n * The row of a keyed each block that is currently updating. We track this\n * so that `animate:` directives have something to attach themselves to\n * @type {EachItem | null}\n */\nexport let current_each_item = null;\n\n/** @param {EachItem | null} item */\nexport function set_current_each_item(item) {\n\tcurrent_each_item = item;\n}\n\n/**\n * @param {any} _\n * @param {number} i\n */\nexport function index(_, i) {\n\treturn i;\n}\n\n/**\n * Pause multiple effects simultaneously, and coordinate their\n * subsequent destruction. Used in each blocks\n * @param {EachState} state\n * @param {EachItem[]} items\n * @param {null | Node} controlled_anchor\n * @param {Map<any, EachItem>} items_map\n */\nfunction pause_effects(state, items, controlled_anchor, items_map) {\n\t/** @type {TransitionManager[]} */\n\tvar transitions = [];\n\tvar length = items.length;\n\n\tfor (var i = 0; i < length; i++) {\n\t\tpause_children(items[i].e, transitions, true);\n\t}\n\n\tvar is_controlled = length > 0 && transitions.length === 0 && controlled_anchor !== null;\n\t// If we have a controlled anchor, it means that the each block is inside a single\n\t// DOM element, so we can apply a fast-path for clearing the contents of the element.\n\tif (is_controlled) {\n\t\tvar parent_node = /** @type {Element} */ (\n\t\t\t/** @type {Element} */ (controlled_anchor).parentNode\n\t\t);\n\t\tclear_text_content(parent_node);\n\t\tparent_node.append(/** @type {Element} */ (controlled_anchor));\n\t\titems_map.clear();\n\t\tlink(state, items[0].prev, items[length - 1].next);\n\t}\n\n\trun_out_transitions(transitions, () => {\n\t\tfor (var i = 0; i < length; i++) {\n\t\t\tvar item = items[i];\n\t\t\tif (!is_controlled) {\n\t\t\t\titems_map.delete(item.k);\n\t\t\t\tlink(state, item.prev, item.next);\n\t\t\t}\n\t\t\tdestroy_effect(item.e, !is_controlled);\n\t\t}\n\t});\n}\n\n/**\n * @template V\n * @param {Element | Comment} node The next sibling node, or the parent node if this is a 'controlled' block\n * @param {number} flags\n * @param {() => V[]} get_collection\n * @param {(value: V, index: number) => any} get_key\n * @param {(anchor: Node, item: MaybeSource<V>, index: MaybeSource<number>) => void} render_fn\n * @param {null | ((anchor: Node) => void)} fallback_fn\n * @returns {void}\n */\nexport function each(node, flags, get_collection, get_key, render_fn, fallback_fn = null) {\n\tvar anchor = node;\n\n\t/** @type {EachState} */\n\tvar state = { flags, items: new Map(), first: null };\n\n\tvar is_controlled = (flags & EACH_IS_CONTROLLED) !== 0;\n\n\tif (is_controlled) {\n\t\tvar parent_node = /** @type {Element} */ (node);\n\n\t\tanchor = hydrating\n\t\t\t? set_hydrate_node(/** @type {Comment | Text} */ (get_first_child(parent_node)))\n\t\t\t: parent_node.appendChild(create_text());\n\t}\n\n\tif (hydrating) {\n\t\thydrate_next();\n\t}\n\n\t/** @type {Effect | null} */\n\tvar fallback = null;\n\n\tvar was_empty = false;\n\n\t// TODO: ideally we could use derived for runes mode but because of the ability\n\t// to use a store which can be mutated, we can't do that here as mutating a store\n\t// will still result in the collection array being the same from the store\n\tvar each_array = derived_safe_equal(() => {\n\t\tvar collection = get_collection();\n\n\t\treturn is_array(collection) ? collection : collection == null ? [] : array_from(collection);\n\t});\n\n\tblock(() => {\n\t\tvar array = get(each_array);\n\t\tvar length = array.length;\n\n\t\tif (was_empty && length === 0) {\n\t\t\t// ignore updates if the array is empty,\n\t\t\t// and it already was empty on previous run\n\t\t\treturn;\n\t\t}\n\t\twas_empty = length === 0;\n\n\t\t/** `true` if there was a hydration mismatch. Needs to be a `let` or else it isn't treeshaken out */\n\t\tlet mismatch = false;\n\n\t\tif (hydrating) {\n\t\t\tvar is_else = read_hydration_instruction(anchor) === HYDRATION_START_ELSE;\n\n\t\t\tif (is_else !== (length === 0)) {\n\t\t\t\t// hydration mismatch — remove the server-rendered DOM and start over\n\t\t\t\tanchor = remove_nodes();\n\n\t\t\t\tset_hydrate_node(anchor);\n\t\t\t\tset_hydrating(false);\n\t\t\t\tmismatch = true;\n\t\t\t}\n\t\t}\n\n\t\t// this is separate to the previous block because `hydrating` might change\n\t\tif (hydrating) {\n\t\t\t/** @type {EachItem | null} */\n\t\t\tvar prev = null;\n\n\t\t\t/** @type {EachItem} */\n\t\t\tvar item;\n\n\t\t\tfor (var i = 0; i < length; i++) {\n\t\t\t\tif (\n\t\t\t\t\thydrate_node.nodeType === 8 &&\n\t\t\t\t\t/** @type {Comment} */ (hydrate_node).data === HYDRATION_END\n\t\t\t\t) {\n\t\t\t\t\t// The server rendered fewer items than expected,\n\t\t\t\t\t// so break out and continue appending non-hydrated items\n\t\t\t\t\tanchor = /** @type {Comment} */ (hydrate_node);\n\t\t\t\t\tmismatch = true;\n\t\t\t\t\tset_hydrating(false);\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\n\t\t\t\tvar value = array[i];\n\t\t\t\tvar key = get_key(value, i);\n\t\t\t\titem = create_item(\n\t\t\t\t\thydrate_node,\n\t\t\t\t\tstate,\n\t\t\t\t\tprev,\n\t\t\t\t\tnull,\n\t\t\t\t\tvalue,\n\t\t\t\t\tkey,\n\t\t\t\t\ti,\n\t\t\t\t\trender_fn,\n\t\t\t\t\tflags,\n\t\t\t\t\tget_collection\n\t\t\t\t);\n\t\t\t\tstate.items.set(key, item);\n\n\t\t\t\tprev = item;\n\t\t\t}\n\n\t\t\t// remove excess nodes\n\t\t\tif (length > 0) {\n\t\t\t\tset_hydrate_node(remove_nodes());\n\t\t\t}\n\t\t}\n\n\t\tif (!hydrating) {\n\t\t\treconcile(array, state, anchor, render_fn, flags, get_key, get_collection);\n\t\t}\n\n\t\tif (fallback_fn !== null) {\n\t\t\tif (length === 0) {\n\t\t\t\tif (fallback) {\n\t\t\t\t\tresume_effect(fallback);\n\t\t\t\t} else {\n\t\t\t\t\tfallback = branch(() => fallback_fn(anchor));\n\t\t\t\t}\n\t\t\t} else if (fallback !== null) {\n\t\t\t\tpause_effect(fallback, () => {\n\t\t\t\t\tfallback = null;\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tif (mismatch) {\n\t\t\t// continue in hydration mode\n\t\t\tset_hydrating(true);\n\t\t}\n\n\t\t// When we mount the each block for the first time, the collection won't be\n\t\t// connected to this effect as the effect hasn't finished running yet and its deps\n\t\t// won't be assigned. However, it's possible that when reconciling the each block\n\t\t// that a mutation occurred and it's made the collection MAYBE_DIRTY, so reading the\n\t\t// collection again can provide consistency to the reactive graph again as the deriveds\n\t\t// will now be `CLEAN`.\n\t\tget(each_array);\n\t});\n\n\tif (hydrating) {\n\t\tanchor = hydrate_node;\n\t}\n}\n\n/**\n * Add, remove, or reorder items output by an each block as its input changes\n * @template V\n * @param {Array<V>} array\n * @param {EachState} state\n * @param {Element | Comment | Text} anchor\n * @param {(anchor: Node, item: MaybeSource<V>, index: number | Source<number>, collection: () => V[]) => void} render_fn\n * @param {number} flags\n * @param {(value: V, index: number) => any} get_key\n * @param {() => V[]} get_collection\n * @returns {void}\n */\nfunction reconcile(array, state, anchor, render_fn, flags, get_key, get_collection) {\n\tvar is_animated = (flags & EACH_IS_ANIMATED) !== 0;\n\tvar should_update = (flags & (EACH_ITEM_REACTIVE | EACH_INDEX_REACTIVE)) !== 0;\n\n\tvar length = array.length;\n\tvar items = state.items;\n\tvar first = state.first;\n\tvar current = first;\n\n\t/** @type {undefined | Set<EachItem>} */\n\tvar seen;\n\n\t/** @type {EachItem | null} */\n\tvar prev = null;\n\n\t/** @type {undefined | Set<EachItem>} */\n\tvar to_animate;\n\n\t/** @type {EachItem[]} */\n\tvar matched = [];\n\n\t/** @type {EachItem[]} */\n\tvar stashed = [];\n\n\t/** @type {V} */\n\tvar value;\n\n\t/** @type {any} */\n\tvar key;\n\n\t/** @type {EachItem | undefined} */\n\tvar item;\n\n\t/** @type {number} */\n\tvar i;\n\n\tif (is_animated) {\n\t\tfor (i = 0; i < length; i += 1) {\n\t\t\tvalue = array[i];\n\t\t\tkey = get_key(value, i);\n\t\t\titem = items.get(key);\n\n\t\t\tif (item !== undefined) {\n\t\t\t\titem.a?.measure();\n\t\t\t\t(to_animate ??= new Set()).add(item);\n\t\t\t}\n\t\t}\n\t}\n\n\tfor (i = 0; i < length; i += 1) {\n\t\tvalue = array[i];\n\t\tkey = get_key(value, i);\n\t\titem = items.get(key);\n\n\t\tif (item === undefined) {\n\t\t\tvar child_anchor = current ? /** @type {TemplateNode} */ (current.e.nodes_start) : anchor;\n\n\t\t\tprev = create_item(\n\t\t\t\tchild_anchor,\n\t\t\t\tstate,\n\t\t\t\tprev,\n\t\t\t\tprev === null ? state.first : prev.next,\n\t\t\t\tvalue,\n\t\t\t\tkey,\n\t\t\t\ti,\n\t\t\t\trender_fn,\n\t\t\t\tflags,\n\t\t\t\tget_collection\n\t\t\t);\n\n\t\t\titems.set(key, prev);\n\n\t\t\tmatched = [];\n\t\t\tstashed = [];\n\n\t\t\tcurrent = prev.next;\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (should_update) {\n\t\t\tupdate_item(item, value, i, flags);\n\t\t}\n\n\t\tif ((item.e.f & INERT) !== 0) {\n\t\t\tresume_effect(item.e);\n\t\t\tif (is_animated) {\n\t\t\t\titem.a?.unfix();\n\t\t\t\t(to_animate ??= new Set()).delete(item);\n\t\t\t}\n\t\t}\n\n\t\tif (item !== current) {\n\t\t\tif (seen !== undefined && seen.has(item)) {\n\t\t\t\tif (matched.length < stashed.length) {\n\t\t\t\t\t// more efficient to move later items to the front\n\t\t\t\t\tvar start = stashed[0];\n\t\t\t\t\tvar j;\n\n\t\t\t\t\tprev = start.prev;\n\n\t\t\t\t\tvar a = matched[0];\n\t\t\t\t\tvar b = matched[matched.length - 1];\n\n\t\t\t\t\tfor (j = 0; j < matched.length; j += 1) {\n\t\t\t\t\t\tmove(matched[j], start, anchor);\n\t\t\t\t\t}\n\n\t\t\t\t\tfor (j = 0; j < stashed.length; j += 1) {\n\t\t\t\t\t\tseen.delete(stashed[j]);\n\t\t\t\t\t}\n\n\t\t\t\t\tlink(state, a.prev, b.next);\n\t\t\t\t\tlink(state, prev, a);\n\t\t\t\t\tlink(state, b, start);\n\n\t\t\t\t\tcurrent = start;\n\t\t\t\t\tprev = b;\n\t\t\t\t\ti -= 1;\n\n\t\t\t\t\tmatched = [];\n\t\t\t\t\tstashed = [];\n\t\t\t\t} else {\n\t\t\t\t\t// more efficient to move earlier items to the back\n\t\t\t\t\tseen.delete(item);\n\t\t\t\t\tmove(item, current, anchor);\n\n\t\t\t\t\tlink(state, item.prev, item.next);\n\t\t\t\t\tlink(state, item, prev === null ? state.first : prev.next);\n\t\t\t\t\tlink(state, prev, item);\n\n\t\t\t\t\tprev = item;\n\t\t\t\t}\n\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tmatched = [];\n\t\t\tstashed = [];\n\n\t\t\twhile (current !== null && current.k !== key) {\n\t\t\t\t// If the each block isn't inert and an item has an effect that is already inert,\n\t\t\t\t// skip over adding it to our seen Set as the item is already being handled\n\t\t\t\tif ((current.e.f & INERT) === 0) {\n\t\t\t\t\t(seen ??= new Set()).add(current);\n\t\t\t\t}\n\t\t\t\tstashed.push(current);\n\t\t\t\tcurrent = current.next;\n\t\t\t}\n\n\t\t\tif (current === null) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\titem = current;\n\t\t}\n\n\t\tmatched.push(item);\n\t\tprev = item;\n\t\tcurrent = item.next;\n\t}\n\n\tif (current !== null || seen !== undefined) {\n\t\tvar to_destroy = seen === undefined ? [] : array_from(seen);\n\n\t\twhile (current !== null) {\n\t\t\t// If the each block isn't inert, then inert effects are currently outroing and will be removed once the transition is finished\n\t\t\tif ((current.e.f & INERT) === 0) {\n\t\t\t\tto_destroy.push(current);\n\t\t\t}\n\t\t\tcurrent = current.next;\n\t\t}\n\n\t\tvar destroy_length = to_destroy.length;\n\n\t\tif (destroy_length > 0) {\n\t\t\tvar controlled_anchor = (flags & EACH_IS_CONTROLLED) !== 0 && length === 0 ? anchor : null;\n\n\t\t\tif (is_animated) {\n\t\t\t\tfor (i = 0; i < destroy_length; i += 1) {\n\t\t\t\t\tto_destroy[i].a?.measure();\n\t\t\t\t}\n\n\t\t\t\tfor (i = 0; i < destroy_length; i += 1) {\n\t\t\t\t\tto_destroy[i].a?.fix();\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tpause_effects(state, to_destroy, controlled_anchor, items);\n\t\t}\n\t}\n\n\tif (is_animated) {\n\t\tqueue_micro_task(() => {\n\t\t\tif (to_animate === undefined) return;\n\t\t\tfor (item of to_animate) {\n\t\t\t\titem.a?.apply();\n\t\t\t}\n\t\t});\n\t}\n\n\t/** @type {Effect} */ (active_effect).first = state.first && state.first.e;\n\t/** @type {Effect} */ (active_effect).last = prev && prev.e;\n}\n\n/**\n * @param {EachItem} item\n * @param {any} value\n * @param {number} index\n * @param {number} type\n * @returns {void}\n */\nfunction update_item(item, value, index, type) {\n\tif ((type & EACH_ITEM_REACTIVE) !== 0) {\n\t\tinternal_set(item.v, value);\n\t}\n\n\tif ((type & EACH_INDEX_REACTIVE) !== 0) {\n\t\tinternal_set(/** @type {Value<number>} */ (item.i), index);\n\t} else {\n\t\titem.i = index;\n\t}\n}\n\n/**\n * @template V\n * @param {Node} anchor\n * @param {EachState} state\n * @param {EachItem | null} prev\n * @param {EachItem | null} next\n * @param {V} value\n * @param {unknown} key\n * @param {number} index\n * @param {(anchor: Node, item: V | Source<V>, index: number | Value<number>, collection: () => V[]) => void} render_fn\n * @param {number} flags\n * @param {() => V[]} get_collection\n * @returns {EachItem}\n */\nfunction create_item(\n\tanchor,\n\tstate,\n\tprev,\n\tnext,\n\tvalue,\n\tkey,\n\tindex,\n\trender_fn,\n\tflags,\n\tget_collection\n) {\n\tvar previous_each_item = current_each_item;\n\tvar reactive = (flags & EACH_ITEM_REACTIVE) !== 0;\n\tvar mutable = (flags & EACH_ITEM_IMMUTABLE) === 0;\n\n\tvar v = reactive ? (mutable ? mutable_source(value) : source(value)) : value;\n\tvar i = (flags & EACH_INDEX_REACTIVE) === 0 ? index : source(index);\n\n\tif (DEV && reactive) {\n\t\t// For tracing purposes, we need to link the source signal we create with the\n\t\t// collection + index so that tracing works as intended\n\t\t/** @type {Value} */ (v).debug = () => {\n\t\t\tvar collection_index = typeof i === 'number' ? index : i.v;\n\t\t\t// eslint-disable-next-line @typescript-eslint/no-unused-expressions\n\t\t\tget_collection()[collection_index];\n\t\t};\n\t}\n\n\t/** @type {EachItem} */\n\tvar item = {\n\t\ti,\n\t\tv,\n\t\tk: key,\n\t\ta: null,\n\t\t// @ts-expect-error\n\t\te: null,\n\t\tprev,\n\t\tnext\n\t};\n\n\tcurrent_each_item = item;\n\n\ttry {\n\t\titem.e = branch(() => render_fn(anchor, v, i, get_collection), hydrating);\n\n\t\titem.e.prev = prev && prev.e;\n\t\titem.e.next = next && next.e;\n\n\t\tif (prev === null) {\n\t\t\tstate.first = item;\n\t\t} else {\n\t\t\tprev.next = item;\n\t\t\tprev.e.next = item.e;\n\t\t}\n\n\t\tif (next !== null) {\n\t\t\tnext.prev = item;\n\t\t\tnext.e.prev = item.e;\n\t\t}\n\n\t\treturn item;\n\t} finally {\n\t\tcurrent_each_item = previous_each_item;\n\t}\n}\n\n/**\n * @param {EachItem} item\n * @param {EachItem | null} next\n * @param {Text | Element | Comment} anchor\n */\nfunction move(item, next, anchor) {\n\tvar end = item.next ? /** @type {TemplateNode} */ (item.next.e.nodes_start) : anchor;\n\n\tvar dest = next ? /** @type {TemplateNode} */ (next.e.nodes_start) : anchor;\n\tvar node = /** @type {TemplateNode} */ (item.e.nodes_start);\n\n\twhile (node !== end) {\n\t\tvar next_node = /** @type {TemplateNode} */ (get_next_sibling(node));\n\t\tdest.before(node);\n\t\tnode = next_node;\n\t}\n}\n\n/**\n * @param {EachState} state\n * @param {EachItem | null} prev\n * @param {EachItem | null} next\n */\nfunction link(state, prev, next) {\n\tif (prev === null) {\n\t\tstate.first = next;\n\t} else {\n\t\tprev.next = next;\n\t\tprev.e.next = next && next.e;\n\t}\n\n\tif (next !== null) {\n\t\tnext.prev = prev;\n\t\tnext.e.prev = prev && prev.e;\n\t}\n}\n", "/** @import { Effect, TemplateNode } from '#client' */\nimport { FILENAME, HYDRATION_ERROR } from '../../../../constants.js';\nimport { remove_effect_dom, template_effect } from '../../reactivity/effects.js';\nimport { hydrate_next, hydrate_node, hydrating, set_hydrate_node } from '../hydration.js';\nimport { create_fragment_from_html } from '../reconciler.js';\nimport { assign_nodes } from '../template.js';\nimport * as w from '../../warnings.js';\nimport { hash, sanitize_location } from '../../../../utils.js';\nimport { DEV } from 'esm-env';\nimport { dev_current_component_function } from '../../context.js';\nimport { get_first_child, get_next_sibling } from '../operations.js';\nimport { active_effect } from '../../runtime.js';\n\n/**\n * @param {Element} element\n * @param {string | null} server_hash\n * @param {string} value\n */\nfunction check_hash(element, server_hash, value) {\n\tif (!server_hash || server_hash === hash(String(value ?? ''))) return;\n\n\tlet location;\n\n\t// @ts-expect-error\n\tconst loc = element.__svelte_meta?.loc;\n\tif (loc) {\n\t\tlocation = `near ${loc.file}:${loc.line}:${loc.column}`;\n\t} else if (dev_current_component_function?.[FILENAME]) {\n\t\tlocation = `in ${dev_current_component_function[FILENAME]}`;\n\t}\n\n\tw.hydration_html_changed(sanitize_location(location));\n}\n\n/**\n * @param {Element | Text | Comment} node\n * @param {() => string} get_value\n * @param {boolean} [svg]\n * @param {boolean} [mathml]\n * @param {boolean} [skip_warning]\n * @returns {void}\n */\nexport function html(node, get_value, svg = false, mathml = false, skip_warning = false) {\n\tvar anchor = node;\n\n\tvar value = '';\n\n\ttemplate_effect(() => {\n\t\tvar effect = /** @type {Effect} */ (active_effect);\n\n\t\tif (value === (value = get_value() ?? '')) {\n\t\t\tif (hydrating) hydrate_next();\n\t\t\treturn;\n\t\t}\n\n\t\tif (effect.nodes_start !== null) {\n\t\t\tremove_effect_dom(effect.nodes_start, /** @type {TemplateNode} */ (effect.nodes_end));\n\t\t\teffect.nodes_start = effect.nodes_end = null;\n\t\t}\n\n\t\tif (value === '') return;\n\n\t\tif (hydrating) {\n\t\t\t// We're deliberately not trying to repair mismatches between server and client,\n\t\t\t// as it's costly and error-prone (and it's an edge case to have a mismatch anyway)\n\t\t\tvar hash = /** @type {Comment} */ (hydrate_node).data;\n\t\t\tvar next = hydrate_next();\n\t\t\tvar last = next;\n\n\t\t\twhile (next !== null && (next.nodeType !== 8 || /** @type {Comment} */ (next).data !== '')) {\n\t\t\t\tlast = next;\n\t\t\t\tnext = /** @type {TemplateNode} */ (get_next_sibling(next));\n\t\t\t}\n\n\t\t\tif (next === null) {\n\t\t\t\tw.hydration_mismatch();\n\t\t\t\tthrow HYDRATION_ERROR;\n\t\t\t}\n\n\t\t\tif (DEV && !skip_warning) {\n\t\t\t\tcheck_hash(/** @type {Element} */ (next.parentNode), hash, value);\n\t\t\t}\n\n\t\t\tassign_nodes(hydrate_node, last);\n\t\t\tanchor = set_hydrate_node(next);\n\t\t\treturn;\n\t\t}\n\n\t\tvar html = value + '';\n\t\tif (svg) html = `<svg>${html}</svg>`;\n\t\telse if (mathml) html = `<math>${html}</math>`;\n\n\t\t// Don't use create_fragment_with_script_from_html here because that would mean script tags are executed.\n\t\t// @html is basically `.innerHTML = ...` and that doesn't execute scripts either due to security reasons.\n\t\t/** @type {DocumentFragment | Element} */\n\t\tvar node = create_fragment_from_html(html);\n\n\t\tif (svg || mathml) {\n\t\t\tnode = /** @type {Element} */ (get_first_child(node));\n\t\t}\n\n\t\tassign_nodes(\n\t\t\t/** @type {TemplateNode} */ (get_first_child(node)),\n\t\t\t/** @type {TemplateNode} */ (node.lastChild)\n\t\t);\n\n\t\tif (svg || mathml) {\n\t\t\twhile (get_first_child(node)) {\n\t\t\t\tanchor.before(/** @type {Node} */ (get_first_child(node)));\n\t\t\t}\n\t\t} else {\n\t\t\tanchor.before(node);\n\t\t}\n\t});\n}\n", "import { hydrate_next, hydrating } from '../hydration.js';\n\n/**\n * @param {Comment} anchor\n * @param {Record<string, any>} $$props\n * @param {string} name\n * @param {Record<string, unknown>} slot_props\n * @param {null | ((anchor: Comment) => void)} fallback_fn\n */\nexport function slot(anchor, $$props, name, slot_props, fallback_fn) {\n\tif (hydrating) {\n\t\thydrate_next();\n\t}\n\n\tvar slot_fn = $$props.$$slots?.[name];\n\t// Interop: Can use snippets to fill slots\n\tvar is_interop = false;\n\tif (slot_fn === true) {\n\t\tslot_fn = $$props[name === 'default' ? 'children' : name];\n\t\tis_interop = true;\n\t}\n\n\tif (slot_fn === undefined) {\n\t\tif (fallback_fn !== null) {\n\t\t\tfallback_fn(anchor);\n\t\t}\n\t} else {\n\t\tslot_fn(anchor, is_interop ? () => slot_props : slot_props);\n\t}\n}\n\n/**\n * @param {Record<string, any>} props\n * @returns {Record<string, boolean>}\n */\nexport function sanitize_slots(props) {\n\t/** @type {Record<string, boolean>} */\n\tconst sanitized = {};\n\tif (props.children) sanitized.default = true;\n\tfor (const key in props.$$slots) {\n\t\tsanitized[key] = true;\n\t}\n\treturn sanitized;\n}\n", "/** @import { TemplateNode } from '#client' */\n/** @import { Getters } from '#shared' */\nimport { is_void } from '../../utils.js';\nimport * as w from './warnings.js';\nimport * as e from './errors.js';\n\nexport { invalid_default_snippet } from './errors.js';\n\n/**\n * @param {() => string} tag_fn\n * @returns {void}\n */\nexport function validate_void_dynamic_element(tag_fn) {\n\tconst tag = tag_fn();\n\tif (tag && is_void(tag)) {\n\t\tw.dynamic_void_element_content(tag);\n\t}\n}\n\n/** @param {() => unknown} tag_fn */\nexport function validate_dynamic_element_tag(tag_fn) {\n\tconst tag = tag_fn();\n\tconst is_string = typeof tag === 'string';\n\tif (tag && !is_string) {\n\t\te.svelte_element_invalid_this_value();\n\t}\n}\n\n/**\n * @param {any} store\n * @param {string} name\n */\nexport function validate_store(store, name) {\n\tif (store != null && typeof store.subscribe !== 'function') {\n\t\te.store_invalid_shape(name);\n\t}\n}\n\n/**\n * @template {() => unknown} T\n * @param {T} fn\n */\nexport function prevent_snippet_stringification(fn) {\n\tfn.toString = () => {\n\t\te.snippet_without_render_tag();\n\t\treturn '';\n\t};\n\treturn fn;\n}\n", "/** @import { Snippet } from 'svelte' */\n/** @import { Effect, TemplateNode } from '#client' */\n/** @import { Getters } from '#shared' */\nimport { EFFECT_TRANSPARENT } from '#client/constants';\nimport { branch, block, destroy_effect, teardown } from '../../reactivity/effects.js';\nimport {\n\tdev_current_component_function,\n\tset_dev_current_component_function\n} from '../../context.js';\nimport { hydrate_next, hydrate_node, hydrating } from '../hydration.js';\nimport { create_fragment_from_html } from '../reconciler.js';\nimport { assign_nodes } from '../template.js';\nimport * as w from '../../warnings.js';\nimport * as e from '../../errors.js';\nimport { DEV } from 'esm-env';\nimport { get_first_child, get_next_sibling } from '../operations.js';\nimport { noop } from '../../../shared/utils.js';\nimport { prevent_snippet_stringification } from '../../../shared/validate.js';\n\n/**\n * @template {(node: TemplateNode, ...args: any[]) => void} SnippetFn\n * @param {TemplateNode} node\n * @param {() => SnippetFn | null | undefined} get_snippet\n * @param {(() => any)[]} args\n * @returns {void}\n */\nexport function snippet(node, get_snippet, ...args) {\n\tvar anchor = node;\n\n\t/** @type {SnippetFn | null | undefined} */\n\t// @ts-ignore\n\tvar snippet = noop;\n\n\t/** @type {Effect | null} */\n\tvar snippet_effect;\n\n\tblock(() => {\n\t\tif (snippet === (snippet = get_snippet())) return;\n\n\t\tif (snippet_effect) {\n\t\t\tdestroy_effect(snippet_effect);\n\t\t\tsnippet_effect = null;\n\t\t}\n\n\t\tif (DEV && snippet == null) {\n\t\t\te.invalid_snippet();\n\t\t}\n\n\t\tsnippet_effect = branch(() => /** @type {SnippetFn} */ (snippet)(anchor, ...args));\n\t}, EFFECT_TRANSPARENT);\n\n\tif (hydrating) {\n\t\tanchor = hydrate_node;\n\t}\n}\n\n/**\n * In development, wrap the snippet function so that it passes validation, and so that the\n * correct component context is set for ownership checks\n * @param {any} component\n * @param {(node: TemplateNode, ...args: any[]) => void} fn\n */\nexport function wrap_snippet(component, fn) {\n\tconst snippet = (/** @type {TemplateNode} */ node, /** @type {any[]} */ ...args) => {\n\t\tvar previous_component_function = dev_current_component_function;\n\t\tset_dev_current_component_function(component);\n\n\t\ttry {\n\t\t\treturn fn(node, ...args);\n\t\t} finally {\n\t\t\tset_dev_current_component_function(previous_component_function);\n\t\t}\n\t};\n\n\tprevent_snippet_stringification(snippet);\n\n\treturn snippet;\n}\n\n/**\n * Create a snippet programmatically\n * @template {unknown[]} Params\n * @param {(...params: Getters<Params>) => {\n *   render: () => string\n *   setup?: (element: Element) => void | (() => void)\n * }} fn\n * @returns {Snippet<Params>}\n */\nexport function createRawSnippet(fn) {\n\t// @ts-expect-error the types are a lie\n\treturn (/** @type {TemplateNode} */ anchor, /** @type {Getters<Params>} */ ...params) => {\n\t\tvar snippet = fn(...params);\n\n\t\t/** @type {Element} */\n\t\tvar element;\n\n\t\tif (hydrating) {\n\t\t\telement = /** @type {Element} */ (hydrate_node);\n\t\t\thydrate_next();\n\t\t} else {\n\t\t\tvar html = snippet.render().trim();\n\t\t\tvar fragment = create_fragment_from_html(html);\n\t\t\telement = /** @type {Element} */ (get_first_child(fragment));\n\n\t\t\tif (DEV && (get_next_sibling(element) !== null || element.nodeType !== 1)) {\n\t\t\t\tw.invalid_raw_snippet_render();\n\t\t\t}\n\n\t\t\tanchor.before(element);\n\t\t}\n\n\t\tconst result = snippet.setup?.(element);\n\t\tassign_nodes(element, element);\n\n\t\tif (typeof result === 'function') {\n\t\t\tteardown(result);\n\t\t}\n\t};\n}\n", "/** @import { TemplateNode, Dom, Effect } from '#client' */\nimport { EFFECT_TRANSPARENT } from '#client/constants';\nimport { block, branch, pause_effect } from '../../reactivity/effects.js';\nimport { hydrate_next, hydrate_node, hydrating } from '../hydration.js';\n\n/**\n * @template P\n * @template {(props: P) => void} C\n * @param {TemplateNode} node\n * @param {() => C} get_component\n * @param {(anchor: TemplateNode, component: C) => Dom | void} render_fn\n * @returns {void}\n */\nexport function component(node, get_component, render_fn) {\n\tif (hydrating) {\n\t\thydrate_next();\n\t}\n\n\tvar anchor = node;\n\n\t/** @type {C} */\n\tvar component;\n\n\t/** @type {Effect | null} */\n\tvar effect;\n\n\tblock(() => {\n\t\tif (component === (component = get_component())) return;\n\n\t\tif (effect) {\n\t\t\tpause_effect(effect);\n\t\t\teffect = null;\n\t\t}\n\n\t\tif (component) {\n\t\t\teffect = branch(() => render_fn(anchor, component));\n\t\t}\n\t}, EFFECT_TRANSPARENT);\n\n\tif (hydrating) {\n\t\tanchor = hydrate_node;\n\t}\n}\n", "/** @import { Effect, TemplateNode } from '#client' */\nimport { FILENAME, NAMESPACE_SVG } from '../../../../constants.js';\nimport {\n\thydrate_next,\n\thydrate_node,\n\thydrating,\n\tset_hydrate_node,\n\tset_hydrating\n} from '../hydration.js';\nimport { create_text, get_first_child } from '../operations.js';\nimport {\n\tblock,\n\tbranch,\n\tdestroy_effect,\n\tpause_effect,\n\tresume_effect\n} from '../../reactivity/effects.js';\nimport { set_should_intro } from '../../render.js';\nimport { current_each_item, set_current_each_item } from './each.js';\nimport { active_effect } from '../../runtime.js';\nimport { component_context } from '../../context.js';\nimport { DEV } from 'esm-env';\nimport { EFFECT_TRANSPARENT } from '#client/constants';\nimport { assign_nodes } from '../template.js';\nimport { is_raw_text_element } from '../../../../utils.js';\n\n/**\n * @param {Comment | Element} node\n * @param {() => string} get_tag\n * @param {boolean} is_svg\n * @param {undefined | ((element: Element, anchor: Node | null) => void)} render_fn,\n * @param {undefined | (() => string)} get_namespace\n * @param {undefined | [number, number]} location\n * @returns {void}\n */\nexport function element(node, get_tag, is_svg, render_fn, get_namespace, location) {\n\tlet was_hydrating = hydrating;\n\n\tif (hydrating) {\n\t\thydrate_next();\n\t}\n\n\tvar filename = DEV && location && component_context?.function[FILENAME];\n\n\t/** @type {string | null} */\n\tvar tag;\n\n\t/** @type {string | null} */\n\tvar current_tag;\n\n\t/** @type {null | Element} */\n\tvar element = null;\n\n\tif (hydrating && hydrate_node.nodeType === 1) {\n\t\telement = /** @type {Element} */ (hydrate_node);\n\t\thydrate_next();\n\t}\n\n\tvar anchor = /** @type {TemplateNode} */ (hydrating ? hydrate_node : node);\n\n\t/** @type {Effect | null} */\n\tvar effect;\n\n\t/**\n\t * The keyed `{#each ...}` item block, if any, that this element is inside.\n\t * We track this so we can set it when changing the element, allowing any\n\t * `animate:` directive to bind itself to the correct block\n\t */\n\tvar each_item_block = current_each_item;\n\n\tblock(() => {\n\t\tconst next_tag = get_tag() || null;\n\t\tvar ns = get_namespace ? get_namespace() : is_svg || next_tag === 'svg' ? NAMESPACE_SVG : null;\n\n\t\t// Assumption: Noone changes the namespace but not the tag (what would that even mean?)\n\t\tif (next_tag === tag) return;\n\n\t\t// See explanation of `each_item_block` above\n\t\tvar previous_each_item = current_each_item;\n\t\tset_current_each_item(each_item_block);\n\n\t\tif (effect) {\n\t\t\tif (next_tag === null) {\n\t\t\t\t// start outro\n\t\t\t\tpause_effect(effect, () => {\n\t\t\t\t\teffect = null;\n\t\t\t\t\tcurrent_tag = null;\n\t\t\t\t});\n\t\t\t} else if (next_tag === current_tag) {\n\t\t\t\t// same tag as is currently rendered — abort outro\n\t\t\t\tresume_effect(effect);\n\t\t\t} else {\n\t\t\t\t// tag is changing — destroy immediately, render contents without intro transitions\n\t\t\t\tdestroy_effect(effect);\n\t\t\t\tset_should_intro(false);\n\t\t\t}\n\t\t}\n\n\t\tif (next_tag && next_tag !== current_tag) {\n\t\t\teffect = branch(() => {\n\t\t\t\telement = hydrating\n\t\t\t\t\t? /** @type {Element} */ (element)\n\t\t\t\t\t: ns\n\t\t\t\t\t\t? document.createElementNS(ns, next_tag)\n\t\t\t\t\t\t: document.createElement(next_tag);\n\n\t\t\t\tif (DEV && location) {\n\t\t\t\t\t// @ts-expect-error\n\t\t\t\t\telement.__svelte_meta = {\n\t\t\t\t\t\tloc: {\n\t\t\t\t\t\t\tfile: filename,\n\t\t\t\t\t\t\tline: location[0],\n\t\t\t\t\t\t\tcolumn: location[1]\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t}\n\n\t\t\t\tassign_nodes(element, element);\n\n\t\t\t\tif (render_fn) {\n\t\t\t\t\tif (hydrating && is_raw_text_element(next_tag)) {\n\t\t\t\t\t\t// prevent hydration glitches\n\t\t\t\t\t\telement.append(document.createComment(''));\n\t\t\t\t\t}\n\n\t\t\t\t\t// If hydrating, use the existing ssr comment as the anchor so that the\n\t\t\t\t\t// inner open and close methods can pick up the existing nodes correctly\n\t\t\t\t\tvar child_anchor = /** @type {TemplateNode} */ (\n\t\t\t\t\t\thydrating ? get_first_child(element) : element.appendChild(create_text())\n\t\t\t\t\t);\n\n\t\t\t\t\tif (hydrating) {\n\t\t\t\t\t\tif (child_anchor === null) {\n\t\t\t\t\t\t\tset_hydrating(false);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tset_hydrate_node(child_anchor);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// `child_anchor` is undefined if this is a void element, but we still\n\t\t\t\t\t// need to call `render_fn` in order to run actions etc. If the element\n\t\t\t\t\t// contains children, it's a user error (which is warned on elsewhere)\n\t\t\t\t\t// and the DOM will be silently discarded\n\t\t\t\t\trender_fn(element, child_anchor);\n\t\t\t\t}\n\n\t\t\t\t// we do this after calling `render_fn` so that child effects don't override `nodes.end`\n\t\t\t\t/** @type {Effect} */ (active_effect).nodes_end = element;\n\n\t\t\t\tanchor.before(element);\n\t\t\t});\n\t\t}\n\n\t\ttag = next_tag;\n\t\tif (tag) current_tag = tag;\n\t\tset_should_intro(true);\n\n\t\tset_current_each_item(previous_each_item);\n\t}, EFFECT_TRANSPARENT);\n\n\tif (was_hydrating) {\n\t\tset_hydrating(true);\n\t\tset_hydrate_node(anchor);\n\t}\n}\n", "import { DEV } from 'esm-env';\nimport { queue_micro_task } from './task.js';\nimport { register_style } from '../dev/css.js';\n\n/**\n * @param {Node} anchor\n * @param {{ hash: string, code: string }} css\n */\nexport function append_styles(anchor, css) {\n\t// Use `queue_micro_task` to ensure `anchor` is in the DOM, otherwise getRootNode() will yield wrong results\n\tqueue_micro_task(() => {\n\t\tvar root = anchor.getRootNode();\n\n\t\tvar target = /** @type {ShadowRoot} */ (root).host\n\t\t\t? /** @type {ShadowRoot} */ (root)\n\t\t\t: /** @type {Document} */ (root).head ?? /** @type {Document} */ (root.ownerDocument).head;\n\n\t\t// Always querying the DOM is roughly the same perf as additionally checking for presence in a map first assuming\n\t\t// that you'll get cache hits half of the time, so we just always query the dom for simplicity and code savings.\n\t\tif (!target.querySelector('#' + css.hash)) {\n\t\t\tconst style = document.createElement('style');\n\t\t\tstyle.id = css.hash;\n\t\t\tstyle.textContent = css.code;\n\n\t\t\ttarget.appendChild(style);\n\n\t\t\tif (DEV) {\n\t\t\t\tregister_style(css.hash, style);\n\t\t\t}\n\t\t}\n\t});\n}\n", "/** @import { ActionPayload } from '#client' */\nimport { effect, render_effect } from '../../reactivity/effects.js';\nimport { safe_not_equal } from '../../reactivity/equality.js';\nimport { deep_read_state, untrack } from '../../runtime.js';\n\n/**\n * @template P\n * @param {Element} dom\n * @param {(dom: Element, value?: P) => ActionPayload<P>} action\n * @param {() => P} [get_value]\n * @returns {void}\n */\nexport function action(dom, action, get_value) {\n\teffect(() => {\n\t\tvar payload = untrack(() => action(dom, get_value?.()) || {});\n\n\t\tif (get_value && payload?.update) {\n\t\t\tvar inited = false;\n\t\t\t/** @type {P} */\n\t\t\tvar prev = /** @type {any} */ ({}); // initialize with something so it's never equal on first run\n\n\t\t\trender_effect(() => {\n\t\t\t\tvar value = get_value();\n\n\t\t\t\t// Action's update method is coarse-grained, i.e. when anything in the passed value changes, update.\n\t\t\t\t// This works in legacy mode because of mutable_source being updated as a whole, but when using $state\n\t\t\t\t// together with actions and mutation, it wouldn't notice the change without a deep read.\n\t\t\t\tdeep_read_state(value);\n\n\t\t\t\tif (inited && safe_not_equal(prev, value)) {\n\t\t\t\t\tprev = value;\n\t\t\t\t\t/** @type {Function} */ (payload.update)(value);\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tinited = true;\n\t\t}\n\n\t\tif (payload?.destroy) {\n\t\t\treturn () => /** @type {Function} */ (payload.destroy)();\n\t\t}\n\t});\n}\n", "/** @import { Effect } from '#client' */\nimport { block, branch, effect, destroy_effect } from '../../reactivity/effects.js';\n\n// TODO in 6.0 or 7.0, when we remove legacy mode, we can simplify this by\n// getting rid of the block/branch stuff and just letting the effect rip.\n// see https://github.com/sveltejs/svelte/pull/15962\n\n/**\n * @param {Element} node\n * @param {() => (node: Element) => void} get_fn\n */\nexport function attach(node, get_fn) {\n\t/** @type {false | undefined | ((node: Element) => void)} */\n\tvar fn = undefined;\n\n\t/** @type {Effect | null} */\n\tvar e;\n\n\tblock(() => {\n\t\tif (fn !== (fn = get_fn())) {\n\t\t\tif (e) {\n\t\t\t\tdestroy_effect(e);\n\t\t\t\te = null;\n\t\t\t}\n\n\t\t\tif (fn) {\n\t\t\t\te = branch(() => {\n\t\t\t\t\teffect(() => /** @type {(node: Element) => void} */ (fn)(node));\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t});\n}\n", "const ATTR_REGEX = /[&\"<]/g;\nconst CONTENT_REGEX = /[&<]/g;\n\n/**\n * @template V\n * @param {V} value\n * @param {boolean} [is_attr]\n */\nexport function escape_html(value, is_attr) {\n\tconst str = String(value ?? '');\n\n\tconst pattern = is_attr ? ATTR_REGEX : CONTENT_REGEX;\n\tpattern.lastIndex = 0;\n\n\tlet escaped = '';\n\tlet last = 0;\n\n\twhile (pattern.test(str)) {\n\t\tconst i = pattern.lastIndex - 1;\n\t\tconst ch = str[i];\n\t\tescaped += str.substring(last, i) + (ch === '&' ? '&amp;' : ch === '\"' ? '&quot;' : '&lt;');\n\t\tlast = i + 1;\n\t}\n\n\treturn escaped + str.substring(last);\n}\n", "import { escape_html } from '../../escaping.js';\nimport { clsx as _clsx } from 'clsx';\n\n/**\n * `<div translate={false}>` should be rendered as `<div translate=\"no\">` and _not_\n * `<div translate=\"false\">`, which is equivalent to `<div translate=\"yes\">`. There\n * may be other odd cases that need to be added to this list in future\n * @type {Record<string, Map<any, string>>}\n */\nconst replacements = {\n\ttranslate: new Map([\n\t\t[true, 'yes'],\n\t\t[false, 'no']\n\t])\n};\n\n/**\n * @template V\n * @param {string} name\n * @param {V} value\n * @param {boolean} [is_boolean]\n * @returns {string}\n */\nexport function attr(name, value, is_boolean = false) {\n\tif (value == null || (!value && is_boolean)) return '';\n\tconst normalized = (name in replacements && replacements[name].get(value)) || value;\n\tconst assignment = is_boolean ? '' : `=\"${escape_html(normalized, true)}\"`;\n\treturn ` ${name}${assignment}`;\n}\n\n/**\n * Small wrapper around clsx to preserve Svelte's (weird) handling of falsy values.\n * TODO Svelte 6 revisit this, and likely turn all falsy values into the empty string (what clsx also does)\n * @param  {any} value\n */\nexport function clsx(value) {\n\tif (typeof value === 'object') {\n\t\treturn _clsx(value);\n\t} else {\n\t\treturn value ?? '';\n\t}\n}\n\nconst whitespace = [...' \\t\\n\\r\\f\\u00a0\\u000b\\ufeff'];\n\n/**\n * @param {any} value\n * @param {string | null} [hash]\n * @param {Record<string, boolean>} [directives]\n * @returns {string | null}\n */\nexport function to_class(value, hash, directives) {\n\tvar classname = value == null ? '' : '' + value;\n\n\tif (hash) {\n\t\tclassname = classname ? classname + ' ' + hash : hash;\n\t}\n\n\tif (directives) {\n\t\tfor (var key in directives) {\n\t\t\tif (directives[key]) {\n\t\t\t\tclassname = classname ? classname + ' ' + key : key;\n\t\t\t} else if (classname.length) {\n\t\t\t\tvar len = key.length;\n\t\t\t\tvar a = 0;\n\n\t\t\t\twhile ((a = classname.indexOf(key, a)) >= 0) {\n\t\t\t\t\tvar b = a + len;\n\n\t\t\t\t\tif (\n\t\t\t\t\t\t(a === 0 || whitespace.includes(classname[a - 1])) &&\n\t\t\t\t\t\t(b === classname.length || whitespace.includes(classname[b]))\n\t\t\t\t\t) {\n\t\t\t\t\t\tclassname = (a === 0 ? '' : classname.substring(0, a)) + classname.substring(b + 1);\n\t\t\t\t\t} else {\n\t\t\t\t\t\ta = b;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn classname === '' ? null : classname;\n}\n\n/**\n *\n * @param {Record<string,any>} styles\n * @param {boolean} important\n */\nfunction append_styles(styles, important = false) {\n\tvar separator = important ? ' !important;' : ';';\n\tvar css = '';\n\n\tfor (var key in styles) {\n\t\tvar value = styles[key];\n\t\tif (value != null && value !== '') {\n\t\t\tcss += ' ' + key + ': ' + value + separator;\n\t\t}\n\t}\n\n\treturn css;\n}\n\n/**\n * @param {string} name\n * @returns {string}\n */\nfunction to_css_name(name) {\n\tif (name[0] !== '-' || name[1] !== '-') {\n\t\treturn name.toLowerCase();\n\t}\n\treturn name;\n}\n\n/**\n * @param {any} value\n * @param {Record<string, any> | [Record<string, any>, Record<string, any>]} [styles]\n * @returns {string | null}\n */\nexport function to_style(value, styles) {\n\tif (styles) {\n\t\tvar new_style = '';\n\n\t\t/** @type {Record<string,any> | undefined} */\n\t\tvar normal_styles;\n\n\t\t/** @type {Record<string,any> | undefined} */\n\t\tvar important_styles;\n\n\t\tif (Array.isArray(styles)) {\n\t\t\tnormal_styles = styles[0];\n\t\t\timportant_styles = styles[1];\n\t\t} else {\n\t\t\tnormal_styles = styles;\n\t\t}\n\n\t\tif (value) {\n\t\t\tvalue = String(value)\n\t\t\t\t.replaceAll(/\\s*\\/\\*.*?\\*\\/\\s*/g, '')\n\t\t\t\t.trim();\n\n\t\t\t/** @type {boolean | '\"' | \"'\"} */\n\t\t\tvar in_str = false;\n\t\t\tvar in_apo = 0;\n\t\t\tvar in_comment = false;\n\n\t\t\tvar reserved_names = [];\n\n\t\t\tif (normal_styles) {\n\t\t\t\treserved_names.push(...Object.keys(normal_styles).map(to_css_name));\n\t\t\t}\n\t\t\tif (important_styles) {\n\t\t\t\treserved_names.push(...Object.keys(important_styles).map(to_css_name));\n\t\t\t}\n\n\t\t\tvar start_index = 0;\n\t\t\tvar name_index = -1;\n\n\t\t\tconst len = value.length;\n\t\t\tfor (var i = 0; i < len; i++) {\n\t\t\t\tvar c = value[i];\n\n\t\t\t\tif (in_comment) {\n\t\t\t\t\tif (c === '/' && value[i - 1] === '*') {\n\t\t\t\t\t\tin_comment = false;\n\t\t\t\t\t}\n\t\t\t\t} else if (in_str) {\n\t\t\t\t\tif (in_str === c) {\n\t\t\t\t\t\tin_str = false;\n\t\t\t\t\t}\n\t\t\t\t} else if (c === '/' && value[i + 1] === '*') {\n\t\t\t\t\tin_comment = true;\n\t\t\t\t} else if (c === '\"' || c === \"'\") {\n\t\t\t\t\tin_str = c;\n\t\t\t\t} else if (c === '(') {\n\t\t\t\t\tin_apo++;\n\t\t\t\t} else if (c === ')') {\n\t\t\t\t\tin_apo--;\n\t\t\t\t}\n\n\t\t\t\tif (!in_comment && in_str === false && in_apo === 0) {\n\t\t\t\t\tif (c === ':' && name_index === -1) {\n\t\t\t\t\t\tname_index = i;\n\t\t\t\t\t} else if (c === ';' || i === len - 1) {\n\t\t\t\t\t\tif (name_index !== -1) {\n\t\t\t\t\t\t\tvar name = to_css_name(value.substring(start_index, name_index).trim());\n\n\t\t\t\t\t\t\tif (!reserved_names.includes(name)) {\n\t\t\t\t\t\t\t\tif (c !== ';') {\n\t\t\t\t\t\t\t\t\ti++;\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\tvar property = value.substring(start_index, i).trim();\n\t\t\t\t\t\t\t\tnew_style += ' ' + property + ';';\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tstart_index = i + 1;\n\t\t\t\t\t\tname_index = -1;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (normal_styles) {\n\t\t\tnew_style += append_styles(normal_styles);\n\t\t}\n\n\t\tif (important_styles) {\n\t\t\tnew_style += append_styles(important_styles, true);\n\t\t}\n\n\t\tnew_style = new_style.trim();\n\t\treturn new_style === '' ? null : new_style;\n\t}\n\n\treturn value == null ? null : String(value);\n}\n", "import { to_class } from '../../../shared/attributes.js';\nimport { hydrating } from '../hydration.js';\n\n/**\n * @param {Element} dom\n * @param {boolean | number} is_html\n * @param {string | null} value\n * @param {string} [hash]\n * @param {Record<string, any>} [prev_classes]\n * @param {Record<string, any>} [next_classes]\n * @returns {Record<string, boolean> | undefined}\n */\nexport function set_class(dom, is_html, value, hash, prev_classes, next_classes) {\n\t// @ts-expect-error need to add __className to patched prototype\n\tvar prev = dom.__className;\n\n\tif (\n\t\thydrating ||\n\t\tprev !== value ||\n\t\tprev === undefined // for edge case of `class={undefined}`\n\t) {\n\t\tvar next_class_name = to_class(value, hash, next_classes);\n\n\t\tif (!hydrating || next_class_name !== dom.getAttribute('class')) {\n\t\t\t// Removing the attribute when the value is only an empty string causes\n\t\t\t// performance issues vs simply making the className an empty string. So\n\t\t\t// we should only remove the class if the value is nullish\n\t\t\t// and there no hash/directives :\n\t\t\tif (next_class_name == null) {\n\t\t\t\tdom.removeAttribute('class');\n\t\t\t} else if (is_html) {\n\t\t\t\tdom.className = next_class_name;\n\t\t\t} else {\n\t\t\t\tdom.setAttribute('class', next_class_name);\n\t\t\t}\n\t\t}\n\n\t\t// @ts-expect-error need to add __className to patched prototype\n\t\tdom.__className = value;\n\t} else if (next_classes && prev_classes !== next_classes) {\n\t\tfor (var key in next_classes) {\n\t\t\tvar is_present = !!next_classes[key];\n\n\t\t\tif (prev_classes == null || is_present !== !!prev_classes[key]) {\n\t\t\t\tdom.classList.toggle(key, is_present);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn next_classes;\n}\n", "import { to_style } from '../../../shared/attributes.js';\nimport { hydrating } from '../hydration.js';\n\n/**\n * @param {Element & ElementCSSInlineStyle} dom\n * @param {Record<string, any>} prev\n * @param {Record<string, any>} next\n * @param {string} [priority]\n */\nfunction update_styles(dom, prev = {}, next, priority) {\n\tfor (var key in next) {\n\t\tvar value = next[key];\n\n\t\tif (prev[key] !== value) {\n\t\t\tif (next[key] == null) {\n\t\t\t\tdom.style.removeProperty(key);\n\t\t\t} else {\n\t\t\t\tdom.style.setProperty(key, value, priority);\n\t\t\t}\n\t\t}\n\t}\n}\n\n/**\n * @param {Element & ElementCSSInlineStyle} dom\n * @param {string | null} value\n * @param {Record<string, any> | [Record<string, any>, Record<string, any>]} [prev_styles]\n * @param {Record<string, any> | [Record<string, any>, Record<string, any>]} [next_styles]\n */\nexport function set_style(dom, value, prev_styles, next_styles) {\n\t// @ts-expect-error\n\tvar prev = dom.__style;\n\n\tif (hydrating || prev !== value) {\n\t\tvar next_style_attr = to_style(value, next_styles);\n\n\t\tif (!hydrating || next_style_attr !== dom.getAttribute('style')) {\n\t\t\tif (next_style_attr == null) {\n\t\t\t\tdom.removeAttribute('style');\n\t\t\t} else {\n\t\t\t\tdom.style.cssText = next_style_attr;\n\t\t\t}\n\t\t}\n\n\t\t// @ts-expect-error\n\t\tdom.__style = value;\n\t} else if (next_styles) {\n\t\tif (Array.isArray(next_styles)) {\n\t\t\tupdate_styles(dom, prev_styles?.[0], next_styles[0]);\n\t\t\tupdate_styles(dom, prev_styles?.[1], next_styles[1], 'important');\n\t\t} else {\n\t\t\tupdate_styles(dom, prev_styles, next_styles);\n\t\t}\n\t}\n\n\treturn next_styles;\n}\n", "import { DEV } from 'esm-env';\nimport { hydrating, set_hydrating } from '../hydration.js';\nimport { get_descriptors, get_prototype_of } from '../../../shared/utils.js';\nimport { create_event, delegate } from './events.js';\nimport { add_form_reset_listener, autofocus } from './misc.js';\nimport * as w from '../../warnings.js';\nimport { LOADING_ATTR_SYMBOL } from '#client/constants';\nimport { queue_idle_task } from '../task.js';\nimport { is_capture_event, is_delegated, normalize_attribute } from '../../../../utils.js';\nimport {\n\tactive_effect,\n\tactive_reaction,\n\tset_active_effect,\n\tset_active_reaction\n} from '../../runtime.js';\nimport { attach } from './attachments.js';\nimport { clsx } from '../../../shared/attributes.js';\nimport { set_class } from './class.js';\nimport { set_style } from './style.js';\nimport { ATTACHMENT_KEY, NAMESPACE_HTML } from '../../../../constants.js';\n\nexport const CLASS = Symbol('class');\nexport const STYLE = Symbol('style');\n\nconst IS_CUSTOM_ELEMENT = Symbol('is custom element');\nconst IS_HTML = Symbol('is html');\n\n/**\n * The value/checked attribute in the template actually corresponds to the defaultValue property, so we need\n * to remove it upon hydration to avoid a bug when someone resets the form value.\n * @param {HTMLInputElement} input\n * @returns {void}\n */\nexport function remove_input_defaults(input) {\n\tif (!hydrating) return;\n\n\tvar already_removed = false;\n\n\t// We try and remove the default attributes later, rather than sync during hydration.\n\t// Doing it sync during hydration has a negative impact on performance, but deferring the\n\t// work in an idle task alleviates this greatly. If a form reset event comes in before\n\t// the idle callback, then we ensure the input defaults are cleared just before.\n\tvar remove_defaults = () => {\n\t\tif (already_removed) return;\n\t\talready_removed = true;\n\n\t\t// Remove the attributes but preserve the values\n\t\tif (input.hasAttribute('value')) {\n\t\t\tvar value = input.value;\n\t\t\tset_attribute(input, 'value', null);\n\t\t\tinput.value = value;\n\t\t}\n\n\t\tif (input.hasAttribute('checked')) {\n\t\t\tvar checked = input.checked;\n\t\t\tset_attribute(input, 'checked', null);\n\t\t\tinput.checked = checked;\n\t\t}\n\t};\n\n\t// @ts-expect-error\n\tinput.__on_r = remove_defaults;\n\tqueue_idle_task(remove_defaults);\n\tadd_form_reset_listener();\n}\n\n/**\n * @param {Element} element\n * @param {any} value\n */\nexport function set_value(element, value) {\n\tvar attributes = get_attributes(element);\n\n\tif (\n\t\tattributes.value ===\n\t\t\t(attributes.value =\n\t\t\t\t// treat null and undefined the same for the initial value\n\t\t\t\tvalue ?? undefined) ||\n\t\t// @ts-expect-error\n\t\t// `progress` elements always need their value set when it's `0`\n\t\t(element.value === value && (value !== 0 || element.nodeName !== 'PROGRESS'))\n\t) {\n\t\treturn;\n\t}\n\n\t// @ts-expect-error\n\telement.value = value ?? '';\n}\n\n/**\n * @param {Element} element\n * @param {boolean} checked\n */\nexport function set_checked(element, checked) {\n\tvar attributes = get_attributes(element);\n\n\tif (\n\t\tattributes.checked ===\n\t\t(attributes.checked =\n\t\t\t// treat null and undefined the same for the initial value\n\t\t\tchecked ?? undefined)\n\t) {\n\t\treturn;\n\t}\n\n\t// @ts-expect-error\n\telement.checked = checked;\n}\n\n/**\n * Sets the `selected` attribute on an `option` element.\n * Not set through the property because that doesn't reflect to the DOM,\n * which means it wouldn't be taken into account when a form is reset.\n * @param {HTMLOptionElement} element\n * @param {boolean} selected\n */\nexport function set_selected(element, selected) {\n\tif (selected) {\n\t\t// The selected option could've changed via user selection, and\n\t\t// setting the value without this check would set it back.\n\t\tif (!element.hasAttribute('selected')) {\n\t\t\telement.setAttribute('selected', '');\n\t\t}\n\t} else {\n\t\telement.removeAttribute('selected');\n\t}\n}\n\n/**\n * Applies the default checked property without influencing the current checked property.\n * @param {HTMLInputElement} element\n * @param {boolean} checked\n */\nexport function set_default_checked(element, checked) {\n\tconst existing_value = element.checked;\n\telement.defaultChecked = checked;\n\telement.checked = existing_value;\n}\n\n/**\n * Applies the default value property without influencing the current value property.\n * @param {HTMLInputElement | HTMLTextAreaElement} element\n * @param {string} value\n */\nexport function set_default_value(element, value) {\n\tconst existing_value = element.value;\n\telement.defaultValue = value;\n\telement.value = existing_value;\n}\n\n/**\n * @param {Element} element\n * @param {string} attribute\n * @param {string | null} value\n * @param {boolean} [skip_warning]\n */\nexport function set_attribute(element, attribute, value, skip_warning) {\n\tvar attributes = get_attributes(element);\n\n\tif (hydrating) {\n\t\tattributes[attribute] = element.getAttribute(attribute);\n\n\t\tif (\n\t\t\tattribute === 'src' ||\n\t\t\tattribute === 'srcset' ||\n\t\t\t(attribute === 'href' && element.nodeName === 'LINK')\n\t\t) {\n\t\t\tif (!skip_warning) {\n\t\t\t\tcheck_src_in_dev_hydration(element, attribute, value ?? '');\n\t\t\t}\n\n\t\t\t// If we reset these attributes, they would result in another network request, which we want to avoid.\n\t\t\t// We assume they are the same between client and server as checking if they are equal is expensive\n\t\t\t// (we can't just compare the strings as they can be different between client and server but result in the\n\t\t\t// same url, so we would need to create hidden anchor elements to compare them)\n\t\t\treturn;\n\t\t}\n\t}\n\n\tif (attributes[attribute] === (attributes[attribute] = value)) return;\n\n\tif (attribute === 'loading') {\n\t\t// @ts-expect-error\n\t\telement[LOADING_ATTR_SYMBOL] = value;\n\t}\n\n\tif (value == null) {\n\t\telement.removeAttribute(attribute);\n\t} else if (typeof value !== 'string' && get_setters(element).includes(attribute)) {\n\t\t// @ts-ignore\n\t\telement[attribute] = value;\n\t} else {\n\t\telement.setAttribute(attribute, value);\n\t}\n}\n\n/**\n * @param {Element} dom\n * @param {string} attribute\n * @param {string} value\n */\nexport function set_xlink_attribute(dom, attribute, value) {\n\tdom.setAttributeNS('http://www.w3.org/1999/xlink', attribute, value);\n}\n\n/**\n * @param {HTMLElement} node\n * @param {string} prop\n * @param {any} value\n */\nexport function set_custom_element_data(node, prop, value) {\n\t// We need to ensure that setting custom element props, which can\n\t// invoke lifecycle methods on other custom elements, does not also\n\t// associate those lifecycle methods with the current active reaction\n\t// or effect\n\tvar previous_reaction = active_reaction;\n\tvar previous_effect = active_effect;\n\n\t// If we're hydrating but the custom element is from Svelte, and it already scaffolded,\n\t// then it might run block logic in hydration mode, which we have to prevent.\n\tlet was_hydrating = hydrating;\n\tif (hydrating) {\n\t\tset_hydrating(false);\n\t}\n\n\tset_active_reaction(null);\n\tset_active_effect(null);\n\n\ttry {\n\t\tif (\n\t\t\t// `style` should use `set_attribute` rather than the setter\n\t\t\tprop !== 'style' &&\n\t\t\t// Don't compute setters for custom elements while they aren't registered yet,\n\t\t\t// because during their upgrade/instantiation they might add more setters.\n\t\t\t// Instead, fall back to a simple \"an object, then set as property\" heuristic.\n\t\t\t(setters_cache.has(node.nodeName) ||\n\t\t\t// customElements may not be available in browser extension contexts\n\t\t\t!customElements ||\n\t\t\tcustomElements.get(node.tagName.toLowerCase())\n\t\t\t\t? get_setters(node).includes(prop)\n\t\t\t\t: value && typeof value === 'object')\n\t\t) {\n\t\t\t// @ts-expect-error\n\t\t\tnode[prop] = value;\n\t\t} else {\n\t\t\t// We did getters etc checks already, stringify before passing to set_attribute\n\t\t\t// to ensure it doesn't invoke the same logic again, and potentially populating\n\t\t\t// the setters cache too early.\n\t\t\tset_attribute(node, prop, value == null ? value : String(value));\n\t\t}\n\t} finally {\n\t\tset_active_reaction(previous_reaction);\n\t\tset_active_effect(previous_effect);\n\t\tif (was_hydrating) {\n\t\t\tset_hydrating(true);\n\t\t}\n\t}\n}\n\n/**\n * Spreads attributes onto a DOM element, taking into account the currently set attributes\n * @param {Element & ElementCSSInlineStyle} element\n * @param {Record<string | symbol, any> | undefined} prev\n * @param {Record<string | symbol, any>} next New attributes - this function mutates this object\n * @param {string} [css_hash]\n * @param {boolean} [skip_warning]\n * @returns {Record<string, any>}\n */\nexport function set_attributes(element, prev, next, css_hash, skip_warning = false) {\n\tvar attributes = get_attributes(element);\n\n\tvar is_custom_element = attributes[IS_CUSTOM_ELEMENT];\n\tvar preserve_attribute_case = !attributes[IS_HTML];\n\n\t// If we're hydrating but the custom element is from Svelte, and it already scaffolded,\n\t// then it might run block logic in hydration mode, which we have to prevent.\n\tlet is_hydrating_custom_element = hydrating && is_custom_element;\n\tif (is_hydrating_custom_element) {\n\t\tset_hydrating(false);\n\t}\n\n\tvar current = prev || {};\n\tvar is_option_element = element.tagName === 'OPTION';\n\n\tfor (var key in prev) {\n\t\tif (!(key in next)) {\n\t\t\tnext[key] = null;\n\t\t}\n\t}\n\n\tif (next.class) {\n\t\tnext.class = clsx(next.class);\n\t} else if (css_hash || next[CLASS]) {\n\t\tnext.class = null; /* force call to set_class() */\n\t}\n\n\tif (next[STYLE]) {\n\t\tnext.style ??= null; /* force call to set_style() */\n\t}\n\n\tvar setters = get_setters(element);\n\n\t// since key is captured we use const\n\tfor (const key in next) {\n\t\t// let instead of var because referenced in a closure\n\t\tlet value = next[key];\n\n\t\t// Up here because we want to do this for the initial value, too, even if it's undefined,\n\t\t// and this wouldn't be reached in case of undefined because of the equality check below\n\t\tif (is_option_element && key === 'value' && value == null) {\n\t\t\t// The <option> element is a special case because removing the value attribute means\n\t\t\t// the value is set to the text content of the option element, and setting the value\n\t\t\t// to null or undefined means the value is set to the string \"null\" or \"undefined\".\n\t\t\t// To align with how we handle this case in non-spread-scenarios, this logic is needed.\n\t\t\t// There's a super-edge-case bug here that is left in in favor of smaller code size:\n\t\t\t// Because of the \"set missing props to null\" logic above, we can't differentiate\n\t\t\t// between a missing value and an explicitly set value of null or undefined. That means\n\t\t\t// that once set, the value attribute of an <option> element can't be removed. This is\n\t\t\t// a very rare edge case, and removing the attribute altogether isn't possible either\n\t\t\t// for the <option value={undefined}> case, so we're not losing any functionality here.\n\t\t\t// @ts-ignore\n\t\t\telement.value = element.__value = '';\n\t\t\tcurrent[key] = value;\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (key === 'class') {\n\t\t\tvar is_html = element.namespaceURI === 'http://www.w3.org/1999/xhtml';\n\t\t\tset_class(element, is_html, value, css_hash, prev?.[CLASS], next[CLASS]);\n\t\t\tcurrent[key] = value;\n\t\t\tcurrent[CLASS] = next[CLASS];\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (key === 'style') {\n\t\t\tset_style(element, value, prev?.[STYLE], next[STYLE]);\n\t\t\tcurrent[key] = value;\n\t\t\tcurrent[STYLE] = next[STYLE];\n\t\t\tcontinue;\n\t\t}\n\n\t\tvar prev_value = current[key];\n\t\tif (value === prev_value) continue;\n\n\t\tcurrent[key] = value;\n\n\t\tvar prefix = key[0] + key[1]; // this is faster than key.slice(0, 2)\n\t\tif (prefix === '$$') continue;\n\n\t\tif (prefix === 'on') {\n\t\t\t/** @type {{ capture?: true }} */\n\t\t\tconst opts = {};\n\t\t\tconst event_handle_key = '$$' + key;\n\t\t\tlet event_name = key.slice(2);\n\t\t\tvar delegated = is_delegated(event_name);\n\n\t\t\tif (is_capture_event(event_name)) {\n\t\t\t\tevent_name = event_name.slice(0, -7);\n\t\t\t\topts.capture = true;\n\t\t\t}\n\n\t\t\tif (!delegated && prev_value) {\n\t\t\t\t// Listening to same event but different handler -> our handle function below takes care of this\n\t\t\t\t// If we were to remove and add listeners in this case, it could happen that the event is \"swallowed\"\n\t\t\t\t// (the browser seems to not know yet that a new one exists now) and doesn't reach the handler\n\t\t\t\t// https://github.com/sveltejs/svelte/issues/11903\n\t\t\t\tif (value != null) continue;\n\n\t\t\t\telement.removeEventListener(event_name, current[event_handle_key], opts);\n\t\t\t\tcurrent[event_handle_key] = null;\n\t\t\t}\n\n\t\t\tif (value != null) {\n\t\t\t\tif (!delegated) {\n\t\t\t\t\t/**\n\t\t\t\t\t * @this {any}\n\t\t\t\t\t * @param {Event} evt\n\t\t\t\t\t */\n\t\t\t\t\tfunction handle(evt) {\n\t\t\t\t\t\tcurrent[key].call(this, evt);\n\t\t\t\t\t}\n\n\t\t\t\t\tcurrent[event_handle_key] = create_event(event_name, element, handle, opts);\n\t\t\t\t} else {\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\telement[`__${event_name}`] = value;\n\t\t\t\t\tdelegate([event_name]);\n\t\t\t\t}\n\t\t\t} else if (delegated) {\n\t\t\t\t// @ts-ignore\n\t\t\t\telement[`__${event_name}`] = undefined;\n\t\t\t}\n\t\t} else if (key === 'style') {\n\t\t\t// avoid using the setter\n\t\t\tset_attribute(element, key, value);\n\t\t} else if (key === 'autofocus') {\n\t\t\tautofocus(/** @type {HTMLElement} */ (element), Boolean(value));\n\t\t} else if (!is_custom_element && (key === '__value' || (key === 'value' && value != null))) {\n\t\t\t// @ts-ignore We're not running this for custom elements because __value is actually\n\t\t\t// how Lit stores the current value on the element, and messing with that would break things.\n\t\t\telement.value = element.__value = value;\n\t\t} else if (key === 'selected' && is_option_element) {\n\t\t\tset_selected(/** @type {HTMLOptionElement} */ (element), value);\n\t\t} else {\n\t\t\tvar name = key;\n\t\t\tif (!preserve_attribute_case) {\n\t\t\t\tname = normalize_attribute(name);\n\t\t\t}\n\n\t\t\tvar is_default = name === 'defaultValue' || name === 'defaultChecked';\n\n\t\t\tif (value == null && !is_custom_element && !is_default) {\n\t\t\t\tattributes[key] = null;\n\n\t\t\t\tif (name === 'value' || name === 'checked') {\n\t\t\t\t\t// removing value/checked also removes defaultValue/defaultChecked — preserve\n\t\t\t\t\tlet input = /** @type {HTMLInputElement} */ (element);\n\t\t\t\t\tconst use_default = prev === undefined;\n\t\t\t\t\tif (name === 'value') {\n\t\t\t\t\t\tlet previous = input.defaultValue;\n\t\t\t\t\t\tinput.removeAttribute(name);\n\t\t\t\t\t\tinput.defaultValue = previous;\n\t\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t\tinput.value = input.__value = use_default ? previous : null;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tlet previous = input.defaultChecked;\n\t\t\t\t\t\tinput.removeAttribute(name);\n\t\t\t\t\t\tinput.defaultChecked = previous;\n\t\t\t\t\t\tinput.checked = use_default ? previous : false;\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\telement.removeAttribute(key);\n\t\t\t\t}\n\t\t\t} else if (\n\t\t\t\tis_default ||\n\t\t\t\t(setters.includes(name) && (is_custom_element || typeof value !== 'string'))\n\t\t\t) {\n\t\t\t\t// @ts-ignore\n\t\t\t\telement[name] = value;\n\t\t\t} else if (typeof value !== 'function') {\n\t\t\t\tset_attribute(element, name, value, skip_warning);\n\t\t\t}\n\t\t}\n\t}\n\n\tif (is_hydrating_custom_element) {\n\t\tset_hydrating(true);\n\t}\n\n\tfor (let symbol of Object.getOwnPropertySymbols(next)) {\n\t\tif (symbol.description === ATTACHMENT_KEY) {\n\t\t\tattach(element, () => next[symbol]);\n\t\t}\n\t}\n\n\treturn current;\n}\n\n/**\n *\n * @param {Element} element\n */\nfunction get_attributes(element) {\n\treturn /** @type {Record<string | symbol, unknown>} **/ (\n\t\t// @ts-expect-error\n\t\telement.__attributes ??= {\n\t\t\t[IS_CUSTOM_ELEMENT]: element.nodeName.includes('-'),\n\t\t\t[IS_HTML]: element.namespaceURI === NAMESPACE_HTML\n\t\t}\n\t);\n}\n\n/** @type {Map<string, string[]>} */\nvar setters_cache = new Map();\n\n/** @param {Element} element */\nfunction get_setters(element) {\n\tvar setters = setters_cache.get(element.nodeName);\n\tif (setters) return setters;\n\tsetters_cache.set(element.nodeName, (setters = []));\n\n\tvar descriptors;\n\tvar proto = element; // In the case of custom elements there might be setters on the instance\n\tvar element_proto = Element.prototype;\n\n\t// Stop at Element, from there on there's only unnecessary setters we're not interested in\n\t// Do not use contructor.name here as that's unreliable in some browser environments\n\twhile (element_proto !== proto) {\n\t\tdescriptors = get_descriptors(proto);\n\n\t\tfor (var key in descriptors) {\n\t\t\tif (descriptors[key].set) {\n\t\t\t\tsetters.push(key);\n\t\t\t}\n\t\t}\n\n\t\tproto = get_prototype_of(proto);\n\t}\n\n\treturn setters;\n}\n\n/**\n * @param {any} element\n * @param {string} attribute\n * @param {string} value\n */\nfunction check_src_in_dev_hydration(element, attribute, value) {\n\tif (!DEV) return;\n\tif (attribute === 'srcset' && srcset_url_equal(element, value)) return;\n\tif (src_url_equal(element.getAttribute(attribute) ?? '', value)) return;\n\n\tw.hydration_attribute_changed(\n\t\tattribute,\n\t\telement.outerHTML.replace(element.innerHTML, element.innerHTML && '...'),\n\t\tString(value)\n\t);\n}\n\n/**\n * @param {string} element_src\n * @param {string} url\n * @returns {boolean}\n */\nfunction src_url_equal(element_src, url) {\n\tif (element_src === url) return true;\n\treturn new URL(element_src, document.baseURI).href === new URL(url, document.baseURI).href;\n}\n\n/** @param {string} srcset */\nfunction split_srcset(srcset) {\n\treturn srcset.split(',').map((src) => src.trim().split(' ').filter(Boolean));\n}\n\n/**\n * @param {HTMLSourceElement | HTMLImageElement} element\n * @param {string} srcset\n * @returns {boolean}\n */\nfunction srcset_url_equal(element, srcset) {\n\tvar element_urls = split_srcset(element.srcset);\n\tvar urls = split_srcset(srcset);\n\n\treturn (\n\t\turls.length === element_urls.length &&\n\t\turls.every(\n\t\t\t([url, width], i) =>\n\t\t\t\twidth === element_urls[i][1] &&\n\t\t\t\t// We need to test both ways because Vite will create an a full URL with\n\t\t\t\t// `new URL(asset, import.meta.url).href` for the client when `base: './'`, and the\n\t\t\t\t// relative URLs inside srcset are not automatically resolved to absolute URLs by\n\t\t\t\t// browsers (in contrast to img.src). This means both SSR and DOM code could\n\t\t\t\t// contain relative or absolute URLs.\n\t\t\t\t(src_url_equal(element_urls[i][0], url) || src_url_equal(url, element_urls[i][0]))\n\t\t)\n\t);\n}\n", "/** @import { Raf } from '#client' */\nimport { noop } from '../shared/utils.js';\n\nimport { BROWSER } from 'esm-env';\n\nconst now = BROWSER ? () => performance.now() : () => Date.now();\n\n/** @type {Raf} */\nexport const raf = {\n\t// don't access requestAnimationFrame eagerly outside method\n\t// this allows basic testing of user code without JSDOM\n\t// bunder will eval and remove ternary when the user's app is built\n\ttick: /** @param {any} _ */ (_) => (BROWSER ? requestAnimationFrame : noop)(_),\n\tnow: () => now(),\n\ttasks: new Set()\n};\n", "/** @import { TaskCallback, Task, TaskEntry } from '#client' */\nimport { raf } from './timing.js';\n\n// TODO move this into timing.js where it probably belongs\n\n/**\n * @returns {void}\n */\nfunction run_tasks() {\n\t// use `raf.now()` instead of the `requestAnimationFrame` callback argument, because\n\t// otherwise things can get wonky https://github.com/sveltejs/svelte/pull/14541\n\tconst now = raf.now();\n\n\traf.tasks.forEach((task) => {\n\t\tif (!task.c(now)) {\n\t\t\traf.tasks.delete(task);\n\t\t\ttask.f();\n\t\t}\n\t});\n\n\tif (raf.tasks.size !== 0) {\n\t\traf.tick(run_tasks);\n\t}\n}\n\n/**\n * Creates a new task that runs on each raf frame\n * until it returns a falsy value or is aborted\n * @param {TaskCallback} callback\n * @returns {Task}\n */\nexport function loop(callback) {\n\t/** @type {TaskEntry} */\n\tlet task;\n\n\tif (raf.tasks.size === 0) {\n\t\traf.tick(run_tasks);\n\t}\n\n\treturn {\n\t\tpromise: new Promise((fulfill) => {\n\t\t\traf.tasks.add((task = { c: callback, f: fulfill }));\n\t\t}),\n\t\tabort() {\n\t\t\traf.tasks.delete(task);\n\t\t}\n\t};\n}\n", "/** @import { AnimateFn, Animation, AnimationConfig, EachItem, Effect, TransitionFn, TransitionManager } from '#client' */\nimport { noop, is_function } from '../../../shared/utils.js';\nimport { effect } from '../../reactivity/effects.js';\nimport {\n\tactive_effect,\n\tactive_reaction,\n\tset_active_effect,\n\tset_active_reaction,\n\tuntrack\n} from '../../runtime.js';\nimport { loop } from '../../loop.js';\nimport { should_intro } from '../../render.js';\nimport { current_each_item } from '../blocks/each.js';\nimport { TRANSITION_GLOBAL, TRANSITION_IN, TRANSITION_OUT } from '../../../../constants.js';\nimport { BLOCK_EFFECT, EFFECT_RAN, EFFECT_TRANSPARENT } from '#client/constants';\nimport { queue_micro_task } from '../task.js';\nimport { without_reactive_context } from './bindings/shared.js';\n\n/**\n * @param {Element} element\n * @param {'introstart' | 'introend' | 'outrostart' | 'outroend'} type\n * @returns {void}\n */\nfunction dispatch_event(element, type) {\n\twithout_reactive_context(() => {\n\t\telement.dispatchEvent(new CustomEvent(type));\n\t});\n}\n\n/**\n * Converts a property to the camel-case format expected by Element.animate(), KeyframeEffect(), and KeyframeEffect.setKeyframes().\n * @param {string} style\n * @returns {string}\n */\nfunction css_property_to_camelcase(style) {\n\t// in compliance with spec\n\tif (style === 'float') return 'cssFloat';\n\tif (style === 'offset') return 'cssOffset';\n\n\t// do not rename custom @properties\n\tif (style.startsWith('--')) return style;\n\n\tconst parts = style.split('-');\n\tif (parts.length === 1) return parts[0];\n\treturn (\n\t\tparts[0] +\n\t\tparts\n\t\t\t.slice(1)\n\t\t\t.map(/** @param {any} word */ (word) => word[0].toUpperCase() + word.slice(1))\n\t\t\t.join('')\n\t);\n}\n\n/**\n * @param {string} css\n * @returns {Keyframe}\n */\nfunction css_to_keyframe(css) {\n\t/** @type {Keyframe} */\n\tconst keyframe = {};\n\tconst parts = css.split(';');\n\tfor (const part of parts) {\n\t\tconst [property, value] = part.split(':');\n\t\tif (!property || value === undefined) break;\n\n\t\tconst formatted_property = css_property_to_camelcase(property.trim());\n\t\tkeyframe[formatted_property] = value.trim();\n\t}\n\treturn keyframe;\n}\n\n/** @param {number} t */\nconst linear = (t) => t;\n\n/**\n * Called inside keyed `{#each ...}` blocks (as `$.animation(...)`). This creates an animation manager\n * and attaches it to the block, so that moves can be animated following reconciliation.\n * @template P\n * @param {Element} element\n * @param {() => AnimateFn<P | undefined>} get_fn\n * @param {(() => P) | null} get_params\n */\nexport function animation(element, get_fn, get_params) {\n\tvar item = /** @type {EachItem} */ (current_each_item);\n\n\t/** @type {DOMRect} */\n\tvar from;\n\n\t/** @type {DOMRect} */\n\tvar to;\n\n\t/** @type {Animation | undefined} */\n\tvar animation;\n\n\t/** @type {null | { position: string, width: string, height: string, transform: string }} */\n\tvar original_styles = null;\n\n\titem.a ??= {\n\t\telement,\n\t\tmeasure() {\n\t\t\tfrom = this.element.getBoundingClientRect();\n\t\t},\n\t\tapply() {\n\t\t\tanimation?.abort();\n\n\t\t\tto = this.element.getBoundingClientRect();\n\n\t\t\tif (\n\t\t\t\tfrom.left !== to.left ||\n\t\t\t\tfrom.right !== to.right ||\n\t\t\t\tfrom.top !== to.top ||\n\t\t\t\tfrom.bottom !== to.bottom\n\t\t\t) {\n\t\t\t\tconst options = get_fn()(this.element, { from, to }, get_params?.());\n\n\t\t\t\tanimation = animate(this.element, options, undefined, 1, () => {\n\t\t\t\t\tanimation?.abort();\n\t\t\t\t\tanimation = undefined;\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\tfix() {\n\t\t\t// If an animation is already running, transforming the element is likely to fail,\n\t\t\t// because the styles applied by the animation take precedence. In the case of crossfade,\n\t\t\t// that means the `translate(...)` of the crossfade transition overrules the `translate(...)`\n\t\t\t// we would apply below, leading to the element jumping somewhere to the top left.\n\t\t\tif (element.getAnimations().length) return;\n\n\t\t\t// It's important to destructure these to get fixed values - the object itself has getters,\n\t\t\t// and changing the style to 'absolute' can for example influence the width.\n\t\t\tvar { position, width, height } = getComputedStyle(element);\n\n\t\t\tif (position !== 'absolute' && position !== 'fixed') {\n\t\t\t\tvar style = /** @type {HTMLElement | SVGElement} */ (element).style;\n\n\t\t\t\toriginal_styles = {\n\t\t\t\t\tposition: style.position,\n\t\t\t\t\twidth: style.width,\n\t\t\t\t\theight: style.height,\n\t\t\t\t\ttransform: style.transform\n\t\t\t\t};\n\n\t\t\t\tstyle.position = 'absolute';\n\t\t\t\tstyle.width = width;\n\t\t\t\tstyle.height = height;\n\t\t\t\tvar to = element.getBoundingClientRect();\n\n\t\t\t\tif (from.left !== to.left || from.top !== to.top) {\n\t\t\t\t\tvar transform = `translate(${from.left - to.left}px, ${from.top - to.top}px)`;\n\t\t\t\t\tstyle.transform = style.transform ? `${style.transform} ${transform}` : transform;\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tunfix() {\n\t\t\tif (original_styles) {\n\t\t\t\tvar style = /** @type {HTMLElement | SVGElement} */ (element).style;\n\n\t\t\t\tstyle.position = original_styles.position;\n\t\t\t\tstyle.width = original_styles.width;\n\t\t\t\tstyle.height = original_styles.height;\n\t\t\t\tstyle.transform = original_styles.transform;\n\t\t\t}\n\t\t}\n\t};\n\n\t// in the case of a `<svelte:element>`, it's possible for `$.animation(...)` to be called\n\t// when an animation manager already exists, if the tag changes. in that case, we need to\n\t// swap out the element rather than creating a new manager, in case it happened at the same\n\t// moment as a reconciliation\n\titem.a.element = element;\n}\n\n/**\n * Called inside block effects as `$.transition(...)`. This creates a transition manager and\n * attaches it to the current effect — later, inside `pause_effect` and `resume_effect`, we\n * use this to create `intro` and `outro` transitions.\n * @template P\n * @param {number} flags\n * @param {HTMLElement} element\n * @param {() => TransitionFn<P | undefined>} get_fn\n * @param {(() => P) | null} get_params\n * @returns {void}\n */\nexport function transition(flags, element, get_fn, get_params) {\n\tvar is_intro = (flags & TRANSITION_IN) !== 0;\n\tvar is_outro = (flags & TRANSITION_OUT) !== 0;\n\tvar is_both = is_intro && is_outro;\n\tvar is_global = (flags & TRANSITION_GLOBAL) !== 0;\n\n\t/** @type {'in' | 'out' | 'both'} */\n\tvar direction = is_both ? 'both' : is_intro ? 'in' : 'out';\n\n\t/** @type {AnimationConfig | ((opts: { direction: 'in' | 'out' }) => AnimationConfig) | undefined} */\n\tvar current_options;\n\n\tvar inert = element.inert;\n\n\t/**\n\t * The default overflow style, stashed so we can revert changes during the transition\n\t * that are necessary to work around a Safari <18 bug\n\t * TODO 6.0 remove this, if older versions of Safari have died out enough\n\t */\n\tvar overflow = element.style.overflow;\n\n\t/** @type {Animation | undefined} */\n\tvar intro;\n\n\t/** @type {Animation | undefined} */\n\tvar outro;\n\n\tfunction get_options() {\n\t\tvar previous_reaction = active_reaction;\n\t\tvar previous_effect = active_effect;\n\t\tset_active_reaction(null);\n\t\tset_active_effect(null);\n\t\ttry {\n\t\t\t// If a transition is still ongoing, we use the existing options rather than generating\n\t\t\t// new ones. This ensures that reversible transitions reverse smoothly, rather than\n\t\t\t// jumping to a new spot because (for example) a different `duration` was used\n\t\t\treturn (current_options ??= get_fn()(element, get_params?.() ?? /** @type {P} */ ({}), {\n\t\t\t\tdirection\n\t\t\t}));\n\t\t} finally {\n\t\t\tset_active_reaction(previous_reaction);\n\t\t\tset_active_effect(previous_effect);\n\t\t}\n\t}\n\n\t/** @type {TransitionManager} */\n\tvar transition = {\n\t\tis_global,\n\t\tin() {\n\t\t\telement.inert = inert;\n\n\t\t\tif (!is_intro) {\n\t\t\t\toutro?.abort();\n\t\t\t\toutro?.reset?.();\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (!is_outro) {\n\t\t\t\t// if we intro then outro then intro again, we want to abort the first intro,\n\t\t\t\t// if it's not a bidirectional transition\n\t\t\t\tintro?.abort();\n\t\t\t}\n\n\t\t\tdispatch_event(element, 'introstart');\n\n\t\t\tintro = animate(element, get_options(), outro, 1, () => {\n\t\t\t\tdispatch_event(element, 'introend');\n\n\t\t\t\t// Ensure we cancel the animation to prevent leaking\n\t\t\t\tintro?.abort();\n\t\t\t\tintro = current_options = undefined;\n\n\t\t\t\telement.style.overflow = overflow;\n\t\t\t});\n\t\t},\n\t\tout(fn) {\n\t\t\tif (!is_outro) {\n\t\t\t\tfn?.();\n\t\t\t\tcurrent_options = undefined;\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\telement.inert = true;\n\n\t\t\tdispatch_event(element, 'outrostart');\n\n\t\t\toutro = animate(element, get_options(), intro, 0, () => {\n\t\t\t\tdispatch_event(element, 'outroend');\n\t\t\t\tfn?.();\n\t\t\t});\n\t\t},\n\t\tstop: () => {\n\t\t\tintro?.abort();\n\t\t\toutro?.abort();\n\t\t}\n\t};\n\n\tvar e = /** @type {Effect} */ (active_effect);\n\n\t(e.transitions ??= []).push(transition);\n\n\t// if this is a local transition, we only want to run it if the parent (branch) effect's\n\t// parent (block) effect is where the state change happened. we can determine that by\n\t// looking at whether the block effect is currently initializing\n\tif (is_intro && should_intro) {\n\t\tvar run = is_global;\n\n\t\tif (!run) {\n\t\t\tvar block = /** @type {Effect | null} */ (e.parent);\n\n\t\t\t// skip over transparent blocks (e.g. snippets, else-if blocks)\n\t\t\twhile (block && (block.f & EFFECT_TRANSPARENT) !== 0) {\n\t\t\t\twhile ((block = block.parent)) {\n\t\t\t\t\tif ((block.f & BLOCK_EFFECT) !== 0) break;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\trun = !block || (block.f & EFFECT_RAN) !== 0;\n\t\t}\n\n\t\tif (run) {\n\t\t\teffect(() => {\n\t\t\t\tuntrack(() => transition.in());\n\t\t\t});\n\t\t}\n\t}\n}\n\n/**\n * Animates an element, according to the provided configuration\n * @param {Element} element\n * @param {AnimationConfig | ((opts: { direction: 'in' | 'out' }) => AnimationConfig)} options\n * @param {Animation | undefined} counterpart The corresponding intro/outro to this outro/intro\n * @param {number} t2 The target `t` value — `1` for intro, `0` for outro\n * @param {(() => void)} on_finish Called after successfully completing the animation\n * @returns {Animation}\n */\nfunction animate(element, options, counterpart, t2, on_finish) {\n\tvar is_intro = t2 === 1;\n\n\tif (is_function(options)) {\n\t\t// In the case of a deferred transition (such as `crossfade`), `option` will be\n\t\t// a function rather than an `AnimationConfig`. We need to call this function\n\t\t// once the DOM has been updated...\n\t\t/** @type {Animation} */\n\t\tvar a;\n\t\tvar aborted = false;\n\n\t\tqueue_micro_task(() => {\n\t\t\tif (aborted) return;\n\t\t\tvar o = options({ direction: is_intro ? 'in' : 'out' });\n\t\t\ta = animate(element, o, counterpart, t2, on_finish);\n\t\t});\n\n\t\t// ...but we want to do so without using `async`/`await` everywhere, so\n\t\t// we return a facade that allows everything to remain synchronous\n\t\treturn {\n\t\t\tabort: () => {\n\t\t\t\taborted = true;\n\t\t\t\ta?.abort();\n\t\t\t},\n\t\t\tdeactivate: () => a.deactivate(),\n\t\t\treset: () => a.reset(),\n\t\t\tt: () => a.t()\n\t\t};\n\t}\n\n\tcounterpart?.deactivate();\n\n\tif (!options?.duration) {\n\t\ton_finish();\n\n\t\treturn {\n\t\t\tabort: noop,\n\t\t\tdeactivate: noop,\n\t\t\treset: noop,\n\t\t\tt: () => t2\n\t\t};\n\t}\n\n\tconst { delay = 0, css, tick, easing = linear } = options;\n\n\tvar keyframes = [];\n\n\tif (is_intro && counterpart === undefined) {\n\t\tif (tick) {\n\t\t\ttick(0, 1); // TODO put in nested effect, to avoid interleaved reads/writes?\n\t\t}\n\n\t\tif (css) {\n\t\t\tvar styles = css_to_keyframe(css(0, 1));\n\t\t\tkeyframes.push(styles, styles);\n\t\t}\n\t}\n\n\tvar get_t = () => 1 - t2;\n\n\t// create a dummy animation that lasts as long as the delay (but with whatever devtools\n\t// multiplier is in effect). in the common case that it is `0`, we keep it anyway so that\n\t// the CSS keyframes aren't created until the DOM is updated\n\tvar animation = element.animate(keyframes, { duration: delay });\n\n\tanimation.onfinish = () => {\n\t\t// for bidirectional transitions, we start from the current position,\n\t\t// rather than doing a full intro/outro\n\t\tvar t1 = counterpart?.t() ?? 1 - t2;\n\t\tcounterpart?.abort();\n\n\t\tvar delta = t2 - t1;\n\t\tvar duration = /** @type {number} */ (options.duration) * Math.abs(delta);\n\t\tvar keyframes = [];\n\n\t\tif (duration > 0) {\n\t\t\t/**\n\t\t\t * Whether or not the CSS includes `overflow: hidden`, in which case we need to\n\t\t\t * add it as an inline style to work around a Safari <18 bug\n\t\t\t * TODO 6.0 remove this, if possible\n\t\t\t */\n\t\t\tvar needs_overflow_hidden = false;\n\n\t\t\tif (css) {\n\t\t\t\tvar n = Math.ceil(duration / (1000 / 60)); // `n` must be an integer, or we risk missing the `t2` value\n\n\t\t\t\tfor (var i = 0; i <= n; i += 1) {\n\t\t\t\t\tvar t = t1 + delta * easing(i / n);\n\t\t\t\t\tvar styles = css_to_keyframe(css(t, 1 - t));\n\t\t\t\t\tkeyframes.push(styles);\n\n\t\t\t\t\tneeds_overflow_hidden ||= styles.overflow === 'hidden';\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (needs_overflow_hidden) {\n\t\t\t\t/** @type {HTMLElement} */ (element).style.overflow = 'hidden';\n\t\t\t}\n\n\t\t\tget_t = () => {\n\t\t\t\tvar time = /** @type {number} */ (\n\t\t\t\t\t/** @type {globalThis.Animation} */ (animation).currentTime\n\t\t\t\t);\n\n\t\t\t\treturn t1 + delta * easing(time / duration);\n\t\t\t};\n\n\t\t\tif (tick) {\n\t\t\t\tloop(() => {\n\t\t\t\t\tif (animation.playState !== 'running') return false;\n\n\t\t\t\t\tvar t = get_t();\n\t\t\t\t\ttick(t, 1 - t);\n\n\t\t\t\t\treturn true;\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tanimation = element.animate(keyframes, { duration, fill: 'forwards' });\n\n\t\tanimation.onfinish = () => {\n\t\t\tget_t = () => t2;\n\t\t\ttick?.(t2, 1 - t2);\n\t\t\ton_finish();\n\t\t};\n\t};\n\n\treturn {\n\t\tabort: () => {\n\t\t\tif (animation) {\n\t\t\t\tanimation.cancel();\n\t\t\t\t// This prevents memory leaks in Chromium\n\t\t\t\tanimation.effect = null;\n\t\t\t\t// This prevents onfinish to be launched after cancel(),\n\t\t\t\t// which can happen in some rare cases\n\t\t\t\t// see https://github.com/sveltejs/svelte/issues/13681\n\t\t\t\tanimation.onfinish = noop;\n\t\t\t}\n\t\t},\n\t\tdeactivate: () => {\n\t\t\ton_finish = noop;\n\t\t},\n\t\treset: () => {\n\t\t\tif (t2 === 0) {\n\t\t\t\ttick?.(1, 0);\n\t\t\t}\n\t\t},\n\t\tt: () => get_t()\n\t};\n}\n", "import { listen } from './shared.js';\n\n/**\n * @param {(activeElement: Element | null) => void} update\n * @returns {void}\n */\nexport function bind_active_element(update) {\n\tlisten(document, ['focusin', 'focusout'], (event) => {\n\t\tif (event && event.type === 'focusout' && /** @type {FocusEvent} */ (event).relatedTarget) {\n\t\t\t// The tests still pass if we remove this, because of JSDOM limitations, but it is necessary\n\t\t\t// to avoid temporarily resetting to `document.body`\n\t\t\treturn;\n\t\t}\n\n\t\tupdate(document.activeElement);\n\t});\n}\n", "import { DEV } from 'esm-env';\nimport { render_effect, teardown } from '../../../reactivity/effects.js';\nimport { listen_to_event_and_reset_event } from './shared.js';\nimport * as e from '../../../errors.js';\nimport { is } from '../../../proxy.js';\nimport { queue_micro_task } from '../../task.js';\nimport { hydrating } from '../../hydration.js';\nimport { untrack } from '../../../runtime.js';\nimport { is_runes } from '../../../context.js';\n\n/**\n * @param {HTMLInputElement} input\n * @param {() => unknown} get\n * @param {(value: unknown) => void} set\n * @returns {void}\n */\nexport function bind_value(input, get, set = get) {\n\tvar runes = is_runes();\n\n\tlisten_to_event_and_reset_event(input, 'input', (is_reset) => {\n\t\tif (DEV && input.type === 'checkbox') {\n\t\t\t// TODO should this happen in prod too?\n\t\t\te.bind_invalid_checkbox_value();\n\t\t}\n\n\t\t/** @type {any} */\n\t\tvar value = is_reset ? input.defaultValue : input.value;\n\t\tvalue = is_numberlike_input(input) ? to_number(value) : value;\n\t\tset(value);\n\n\t\t// In runes mode, respect any validation in accessors (doesn't apply in legacy mode,\n\t\t// because we use mutable state which ensures the render effect always runs)\n\t\tif (runes && value !== (value = get())) {\n\t\t\tvar start = input.selectionStart;\n\t\t\tvar end = input.selectionEnd;\n\n\t\t\t// the value is coerced on assignment\n\t\t\tinput.value = value ?? '';\n\n\t\t\t// Restore selection\n\t\t\tif (end !== null) {\n\t\t\t\tinput.selectionStart = start;\n\t\t\t\tinput.selectionEnd = Math.min(end, input.value.length);\n\t\t\t}\n\t\t}\n\t});\n\n\tif (\n\t\t// If we are hydrating and the value has since changed,\n\t\t// then use the updated value from the input instead.\n\t\t(hydrating && input.defaultValue !== input.value) ||\n\t\t// If defaultValue is set, then value == defaultValue\n\t\t// TODO Svelte 6: remove input.value check and set to empty string?\n\t\t(untrack(get) == null && input.value)\n\t) {\n\t\tset(is_numberlike_input(input) ? to_number(input.value) : input.value);\n\t}\n\n\trender_effect(() => {\n\t\tif (DEV && input.type === 'checkbox') {\n\t\t\t// TODO should this happen in prod too?\n\t\t\te.bind_invalid_checkbox_value();\n\t\t}\n\n\t\tvar value = get();\n\n\t\tif (is_numberlike_input(input) && value === to_number(input.value)) {\n\t\t\t// handles 0 vs 00 case (see https://github.com/sveltejs/svelte/issues/9959)\n\t\t\treturn;\n\t\t}\n\n\t\tif (input.type === 'date' && !value && !input.value) {\n\t\t\t// Handles the case where a temporarily invalid date is set (while typing, for example with a leading 0 for the day)\n\t\t\t// and prevents this state from clearing the other parts of the date input (see https://github.com/sveltejs/svelte/issues/7897)\n\t\t\treturn;\n\t\t}\n\n\t\t// don't set the value of the input if it's the same to allow\n\t\t// minlength to work properly\n\t\tif (value !== input.value) {\n\t\t\t// @ts-expect-error the value is coerced on assignment\n\t\t\tinput.value = value ?? '';\n\t\t}\n\t});\n}\n\n/** @type {Set<HTMLInputElement[]>} */\nconst pending = new Set();\n\n/**\n * @param {HTMLInputElement[]} inputs\n * @param {null | [number]} group_index\n * @param {HTMLInputElement} input\n * @param {() => unknown} get\n * @param {(value: unknown) => void} set\n * @returns {void}\n */\nexport function bind_group(inputs, group_index, input, get, set = get) {\n\tvar is_checkbox = input.getAttribute('type') === 'checkbox';\n\tvar binding_group = inputs;\n\n\t// needs to be let or related code isn't treeshaken out if it's always false\n\tlet hydration_mismatch = false;\n\n\tif (group_index !== null) {\n\t\tfor (var index of group_index) {\n\t\t\t// @ts-expect-error\n\t\t\tbinding_group = binding_group[index] ??= [];\n\t\t}\n\t}\n\n\tbinding_group.push(input);\n\n\tlisten_to_event_and_reset_event(\n\t\tinput,\n\t\t'change',\n\t\t() => {\n\t\t\t// @ts-ignore\n\t\t\tvar value = input.__value;\n\n\t\t\tif (is_checkbox) {\n\t\t\t\tvalue = get_binding_group_value(binding_group, value, input.checked);\n\t\t\t}\n\n\t\t\tset(value);\n\t\t},\n\t\t// TODO better default value handling\n\t\t() => set(is_checkbox ? [] : null)\n\t);\n\n\trender_effect(() => {\n\t\tvar value = get();\n\n\t\t// If we are hydrating and the value has since changed, then use the update value\n\t\t// from the input instead.\n\t\tif (hydrating && input.defaultChecked !== input.checked) {\n\t\t\thydration_mismatch = true;\n\t\t\treturn;\n\t\t}\n\n\t\tif (is_checkbox) {\n\t\t\tvalue = value || [];\n\t\t\t// @ts-ignore\n\t\t\tinput.checked = value.includes(input.__value);\n\t\t} else {\n\t\t\t// @ts-ignore\n\t\t\tinput.checked = is(input.__value, value);\n\t\t}\n\t});\n\n\tteardown(() => {\n\t\tvar index = binding_group.indexOf(input);\n\n\t\tif (index !== -1) {\n\t\t\tbinding_group.splice(index, 1);\n\t\t}\n\t});\n\n\tif (!pending.has(binding_group)) {\n\t\tpending.add(binding_group);\n\n\t\tqueue_micro_task(() => {\n\t\t\t// necessary to maintain binding group order in all insertion scenarios\n\t\t\tbinding_group.sort((a, b) => (a.compareDocumentPosition(b) === 4 ? -1 : 1));\n\t\t\tpending.delete(binding_group);\n\t\t});\n\t}\n\n\tqueue_micro_task(() => {\n\t\tif (hydration_mismatch) {\n\t\t\tvar value;\n\n\t\t\tif (is_checkbox) {\n\t\t\t\tvalue = get_binding_group_value(binding_group, value, input.checked);\n\t\t\t} else {\n\t\t\t\tvar hydration_input = binding_group.find((input) => input.checked);\n\t\t\t\t// @ts-ignore\n\t\t\t\tvalue = hydration_input?.__value;\n\t\t\t}\n\n\t\t\tset(value);\n\t\t}\n\t});\n}\n\n/**\n * @param {HTMLInputElement} input\n * @param {() => unknown} get\n * @param {(value: unknown) => void} set\n * @returns {void}\n */\nexport function bind_checked(input, get, set = get) {\n\tlisten_to_event_and_reset_event(input, 'change', (is_reset) => {\n\t\tvar value = is_reset ? input.defaultChecked : input.checked;\n\t\tset(value);\n\t});\n\n\tif (\n\t\t// If we are hydrating and the value has since changed,\n\t\t// then use the update value from the input instead.\n\t\t(hydrating && input.defaultChecked !== input.checked) ||\n\t\t// If defaultChecked is set, then checked == defaultChecked\n\t\tuntrack(get) == null\n\t) {\n\t\tset(input.checked);\n\t}\n\n\trender_effect(() => {\n\t\tvar value = get();\n\t\tinput.checked = Boolean(value);\n\t});\n}\n\n/**\n * @template V\n * @param {Array<HTMLInputElement>} group\n * @param {V} __value\n * @param {boolean} checked\n * @returns {V[]}\n */\nfunction get_binding_group_value(group, __value, checked) {\n\tvar value = new Set();\n\n\tfor (var i = 0; i < group.length; i += 1) {\n\t\tif (group[i].checked) {\n\t\t\t// @ts-ignore\n\t\t\tvalue.add(group[i].__value);\n\t\t}\n\t}\n\n\tif (!checked) {\n\t\tvalue.delete(__value);\n\t}\n\n\treturn Array.from(value);\n}\n\n/**\n * @param {HTMLInputElement} input\n */\nfunction is_numberlike_input(input) {\n\tvar type = input.type;\n\treturn type === 'number' || type === 'range';\n}\n\n/**\n * @param {string} value\n */\nfunction to_number(value) {\n\treturn value === '' ? null : +value;\n}\n\n/**\n * @param {HTMLInputElement} input\n * @param {() => FileList | null} get\n * @param {(value: FileList | null) => void} set\n */\nexport function bind_files(input, get, set = get) {\n\tlisten_to_event_and_reset_event(input, 'change', () => {\n\t\tset(input.files);\n\t});\n\n\tif (\n\t\t// If we are hydrating and the value has since changed,\n\t\t// then use the updated value from the input instead.\n\t\thydrating &&\n\t\tinput.files\n\t) {\n\t\tset(input.files);\n\t}\n\n\trender_effect(() => {\n\t\tinput.files = get();\n\t});\n}\n", "import { render_effect, effect, teardown } from '../../../reactivity/effects.js';\nimport { listen } from './shared.js';\n\n/** @param {TimeRanges} ranges */\nfunction time_ranges_to_array(ranges) {\n\tvar array = [];\n\n\tfor (var i = 0; i < ranges.length; i += 1) {\n\t\tarray.push({ start: ranges.start(i), end: ranges.end(i) });\n\t}\n\n\treturn array;\n}\n\n/**\n * @param {HTMLVideoElement | HTMLAudioElement} media\n * @param {() => number | undefined} get\n * @param {(value: number) => void} set\n * @returns {void}\n */\nexport function bind_current_time(media, get, set = get) {\n\t/** @type {number} */\n\tvar raf_id;\n\t/** @type {number} */\n\tvar value;\n\n\t// Ideally, listening to timeupdate would be enough, but it fires too infrequently for the currentTime\n\t// binding, which is why we use a raf loop, too. We additionally still listen to timeupdate because\n\t// the user could be scrubbing through the video using the native controls when the media is paused.\n\tvar callback = () => {\n\t\tcancelAnimationFrame(raf_id);\n\n\t\tif (!media.paused) {\n\t\t\traf_id = requestAnimationFrame(callback);\n\t\t}\n\n\t\tvar next_value = media.currentTime;\n\t\tif (value !== next_value) {\n\t\t\tset((value = next_value));\n\t\t}\n\t};\n\n\traf_id = requestAnimationFrame(callback);\n\tmedia.addEventListener('timeupdate', callback);\n\n\trender_effect(() => {\n\t\tvar next_value = Number(get());\n\n\t\tif (value !== next_value && !isNaN(/** @type {any} */ (next_value))) {\n\t\t\tmedia.currentTime = value = next_value;\n\t\t}\n\t});\n\n\tteardown(() => {\n\t\tcancelAnimationFrame(raf_id);\n\t\tmedia.removeEventListener('timeupdate', callback);\n\t});\n}\n\n/**\n * @param {HTMLVideoElement | HTMLAudioElement} media\n * @param {(array: Array<{ start: number; end: number }>) => void} set\n */\nexport function bind_buffered(media, set) {\n\t/** @type {{ start: number; end: number; }[]} */\n\tvar current;\n\n\t// `buffered` can update without emitting any event, so we check it on various events.\n\t// By specs, `buffered` always returns a new object, so we have to compare deeply.\n\tlisten(media, ['loadedmetadata', 'progress', 'timeupdate', 'seeking'], () => {\n\t\tvar ranges = media.buffered;\n\n\t\tif (\n\t\t\t!current ||\n\t\t\tcurrent.length !== ranges.length ||\n\t\t\tcurrent.some((range, i) => ranges.start(i) !== range.start || ranges.end(i) !== range.end)\n\t\t) {\n\t\t\tcurrent = time_ranges_to_array(ranges);\n\t\t\tset(current);\n\t\t}\n\t});\n}\n\n/**\n * @param {HTMLVideoElement | HTMLAudioElement} media\n * @param {(array: Array<{ start: number; end: number }>) => void} set\n */\nexport function bind_seekable(media, set) {\n\tlisten(media, ['loadedmetadata'], () => set(time_ranges_to_array(media.seekable)));\n}\n\n/**\n * @param {HTMLVideoElement | HTMLAudioElement} media\n * @param {(array: Array<{ start: number; end: number }>) => void} set\n */\nexport function bind_played(media, set) {\n\tlisten(media, ['timeupdate'], () => set(time_ranges_to_array(media.played)));\n}\n\n/**\n * @param {HTMLVideoElement | HTMLAudioElement} media\n * @param {(seeking: boolean) => void} set\n */\nexport function bind_seeking(media, set) {\n\tlisten(media, ['seeking', 'seeked'], () => set(media.seeking));\n}\n\n/**\n * @param {HTMLVideoElement | HTMLAudioElement} media\n * @param {(seeking: boolean) => void} set\n */\nexport function bind_ended(media, set) {\n\tlisten(media, ['timeupdate', 'ended'], () => set(media.ended));\n}\n\n/**\n * @param {HTMLVideoElement | HTMLAudioElement} media\n * @param {(ready_state: number) => void} set\n */\nexport function bind_ready_state(media, set) {\n\tlisten(\n\t\tmedia,\n\t\t['loadedmetadata', 'loadeddata', 'canplay', 'canplaythrough', 'playing', 'waiting', 'emptied'],\n\t\t() => set(media.readyState)\n\t);\n}\n\n/**\n * @param {HTMLVideoElement | HTMLAudioElement} media\n * @param {() => number | undefined} get\n * @param {(playback_rate: number) => void} set\n */\nexport function bind_playback_rate(media, get, set = get) {\n\t// Needs to happen after element is inserted into the dom (which is guaranteed by using effect),\n\t// else playback will be set back to 1 by the browser\n\teffect(() => {\n\t\tvar value = Number(get());\n\n\t\tif (value !== media.playbackRate && !isNaN(value)) {\n\t\t\tmedia.playbackRate = value;\n\t\t}\n\t});\n\n\t// Start listening to ratechange events after the element is inserted into the dom,\n\t// else playback will be set to 1 by the browser\n\teffect(() => {\n\t\tlisten(media, ['ratechange'], () => {\n\t\t\tset(media.playbackRate);\n\t\t});\n\t});\n}\n\n/**\n * @param {HTMLVideoElement | HTMLAudioElement} media\n * @param {() => boolean | undefined} get\n * @param {(paused: boolean) => void} set\n */\nexport function bind_paused(media, get, set = get) {\n\tvar paused = get();\n\n\tvar update = () => {\n\t\tif (paused !== media.paused) {\n\t\t\tset((paused = media.paused));\n\t\t}\n\t};\n\n\t// If someone switches the src while media is playing, the player will pause.\n\t// Listen to the canplay event to get notified of this situation.\n\tlisten(media, ['play', 'pause', 'canplay'], update, paused == null);\n\n\t// Needs to be an effect to ensure media element is mounted: else, if paused is `false` (i.e. should play right away)\n\t// a \"The play() request was interrupted by a new load request\" error would be thrown because the resource isn't loaded yet.\n\teffect(() => {\n\t\tif ((paused = !!get()) !== media.paused) {\n\t\t\tif (paused) {\n\t\t\t\tmedia.pause();\n\t\t\t} else {\n\t\t\t\tmedia.play().catch(() => {\n\t\t\t\t\tset((paused = true));\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t});\n}\n\n/**\n * @param {HTMLVideoElement | HTMLAudioElement} media\n * @param {() => number | undefined} get\n * @param {(volume: number) => void} set\n */\nexport function bind_volume(media, get, set = get) {\n\tvar callback = () => {\n\t\tset(media.volume);\n\t};\n\n\tif (get() == null) {\n\t\tcallback();\n\t}\n\n\tlisten(media, ['volumechange'], callback, false);\n\n\trender_effect(() => {\n\t\tvar value = Number(get());\n\n\t\tif (value !== media.volume && !isNaN(value)) {\n\t\t\tmedia.volume = value;\n\t\t}\n\t});\n}\n\n/**\n * @param {HTMLVideoElement | HTMLAudioElement} media\n * @param {() => boolean | undefined} get\n * @param {(muted: boolean) => void} set\n */\nexport function bind_muted(media, get, set = get) {\n\tvar callback = () => {\n\t\tset(media.muted);\n\t};\n\n\tif (get() == null) {\n\t\tcallback();\n\t}\n\n\tlisten(media, ['volumechange'], callback, false);\n\n\trender_effect(() => {\n\t\tvar value = !!get();\n\n\t\tif (media.muted !== value) media.muted = value;\n\t});\n}\n", "import { listen } from './shared.js';\n\n/**\n * @param {(online: boolean) => void} update\n * @returns {void}\n */\nexport function bind_online(update) {\n\tlisten(window, ['online', 'offline'], () => {\n\t\tupdate(navigator.onLine);\n\t});\n}\n", "import { teardown } from '../../../reactivity/effects.js';\nimport { get_descriptor } from '../../../../shared/utils.js';\n\n/**\n * Makes an `export`ed (non-prop) variable available on the `$$props` object\n * so that consumers can do `bind:x` on the component.\n * @template V\n * @param {Record<string, unknown>} props\n * @param {string} prop\n * @param {V} value\n * @returns {void}\n */\nexport function bind_prop(props, prop, value) {\n\tvar desc = get_descriptor(props, prop);\n\n\tif (desc && desc.set) {\n\t\tprops[prop] = value;\n\t\tteardown(() => {\n\t\t\tprops[prop] = null;\n\t\t});\n\t}\n}\n", "import { effect } from '../../../reactivity/effects.js';\nimport { listen_to_event_and_reset_event } from './shared.js';\nimport { untrack } from '../../../runtime.js';\nimport { is } from '../../../proxy.js';\nimport { is_array } from '../../../../shared/utils.js';\nimport * as w from '../../../warnings.js';\n\n/**\n * Selects the correct option(s) (depending on whether this is a multiple select)\n * @template V\n * @param {HTMLSelectElement} select\n * @param {V} value\n * @param {boolean} [mounting]\n */\nexport function select_option(select, value, mounting) {\n\tif (select.multiple) {\n\t\t// If value is null or undefined, keep the selection as is\n\t\tif (value == undefined) {\n\t\t\treturn;\n\t\t}\n\n\t\t// If not an array, warn and keep the selection as is\n\t\tif (!is_array(value)) {\n\t\t\treturn w.select_multiple_invalid_value();\n\t\t}\n\n\t\t// Otherwise, update the selection\n\t\treturn select_options(select, value);\n\t}\n\n\tfor (var option of select.options) {\n\t\tvar option_value = get_option_value(option);\n\t\tif (is(option_value, value)) {\n\t\t\toption.selected = true;\n\t\t\treturn;\n\t\t}\n\t}\n\n\tif (!mounting || value !== undefined) {\n\t\tselect.selectedIndex = -1; // no option should be selected\n\t}\n}\n\n/**\n * Selects the correct option(s) if `value` is given,\n * and then sets up a mutation observer to sync the\n * current selection to the dom when it changes. Such\n * changes could for example occur when options are\n * inside an `#each` block.\n * @template V\n * @param {HTMLSelectElement} select\n * @param {() => V} [get_value]\n */\nexport function init_select(select, get_value) {\n\tlet mounting = true;\n\teffect(() => {\n\t\tif (get_value) {\n\t\t\tselect_option(select, untrack(get_value), mounting);\n\t\t}\n\t\tmounting = false;\n\n\t\tvar observer = new MutationObserver(() => {\n\t\t\t// @ts-ignore\n\t\t\tvar value = select.__value;\n\t\t\tselect_option(select, value);\n\t\t\t// Deliberately don't update the potential binding value,\n\t\t\t// the model should be preserved unless explicitly changed\n\t\t});\n\n\t\tobserver.observe(select, {\n\t\t\t// Listen to option element changes\n\t\t\tchildList: true,\n\t\t\tsubtree: true, // because of <optgroup>\n\t\t\t// Listen to option element value attribute changes\n\t\t\t// (doesn't get notified of select value changes,\n\t\t\t// because that property is not reflected as an attribute)\n\t\t\tattributes: true,\n\t\t\tattributeFilter: ['value']\n\t\t});\n\n\t\treturn () => {\n\t\t\tobserver.disconnect();\n\t\t};\n\t});\n}\n\n/**\n * @param {HTMLSelectElement} select\n * @param {() => unknown} get\n * @param {(value: unknown) => void} set\n * @returns {void}\n */\nexport function bind_select_value(select, get, set = get) {\n\tvar mounting = true;\n\n\tlisten_to_event_and_reset_event(select, 'change', (is_reset) => {\n\t\tvar query = is_reset ? '[selected]' : ':checked';\n\t\t/** @type {unknown} */\n\t\tvar value;\n\n\t\tif (select.multiple) {\n\t\t\tvalue = [].map.call(select.querySelectorAll(query), get_option_value);\n\t\t} else {\n\t\t\t/** @type {HTMLOptionElement | null} */\n\t\t\tvar selected_option =\n\t\t\t\tselect.querySelector(query) ??\n\t\t\t\t// will fall back to first non-disabled option if no option is selected\n\t\t\t\tselect.querySelector('option:not([disabled])');\n\t\t\tvalue = selected_option && get_option_value(selected_option);\n\t\t}\n\n\t\tset(value);\n\t});\n\n\t// Needs to be an effect, not a render_effect, so that in case of each loops the logic runs after the each block has updated\n\teffect(() => {\n\t\tvar value = get();\n\t\tselect_option(select, value, mounting);\n\n\t\t// Mounting and value undefined -> take selection from dom\n\t\tif (mounting && value === undefined) {\n\t\t\t/** @type {HTMLOptionElement | null} */\n\t\t\tvar selected_option = select.querySelector(':checked');\n\t\t\tif (selected_option !== null) {\n\t\t\t\tvalue = get_option_value(selected_option);\n\t\t\t\tset(value);\n\t\t\t}\n\t\t}\n\n\t\t// @ts-ignore\n\t\tselect.__value = value;\n\t\tmounting = false;\n\t});\n\n\t// don't pass get_value, we already initialize it in the effect above\n\tinit_select(select);\n}\n\n/**\n * @param {HTMLSelectElement} select\n * @param {unknown[]} value\n */\nfunction select_options(select, value) {\n\tfor (var option of select.options) {\n\t\toption.selected = value.includes(get_option_value(option));\n\t}\n}\n\n/** @param {HTMLOptionElement} option */\nfunction get_option_value(option) {\n\t// __value only exists if the <option> has a value attribute\n\tif ('__value' in option) {\n\t\treturn option.__value;\n\t} else {\n\t\treturn option.value;\n\t}\n}\n", "import { effect, teardown } from '../../../reactivity/effects.js';\nimport { untrack } from '../../../runtime.js';\n\n/**\n * Resize observer singleton.\n * One listener per element only!\n * https://groups.google.com/a/chromium.org/g/blink-dev/c/z6ienONUb5A/m/F5-VcUZtBAAJ\n */\nclass ResizeObserverSingleton {\n\t/** */\n\t#listeners = new WeakMap();\n\n\t/** @type {ResizeObserver | undefined} */\n\t#observer;\n\n\t/** @type {ResizeObserverOptions} */\n\t#options;\n\n\t/** @static */\n\tstatic entries = new WeakMap();\n\n\t/** @param {ResizeObserverOptions} options */\n\tconstructor(options) {\n\t\tthis.#options = options;\n\t}\n\n\t/**\n\t * @param {Element} element\n\t * @param {(entry: ResizeObserverEntry) => any} listener\n\t */\n\tobserve(element, listener) {\n\t\tvar listeners = this.#listeners.get(element) || new Set();\n\t\tlisteners.add(listener);\n\n\t\tthis.#listeners.set(element, listeners);\n\t\tthis.#getObserver().observe(element, this.#options);\n\n\t\treturn () => {\n\t\t\tvar listeners = this.#listeners.get(element);\n\t\t\tlisteners.delete(listener);\n\n\t\t\tif (listeners.size === 0) {\n\t\t\t\tthis.#listeners.delete(element);\n\t\t\t\t/** @type {ResizeObserver} */ (this.#observer).unobserve(element);\n\t\t\t}\n\t\t};\n\t}\n\n\t#getObserver() {\n\t\treturn (\n\t\t\tthis.#observer ??\n\t\t\t(this.#observer = new ResizeObserver(\n\t\t\t\t/** @param {any} entries */ (entries) => {\n\t\t\t\t\tfor (var entry of entries) {\n\t\t\t\t\t\tResizeObserverSingleton.entries.set(entry.target, entry);\n\t\t\t\t\t\tfor (var listener of this.#listeners.get(entry.target) || []) {\n\t\t\t\t\t\t\tlistener(entry);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t))\n\t\t);\n\t}\n}\n\nvar resize_observer_content_box = /* @__PURE__ */ new ResizeObserverSingleton({\n\tbox: 'content-box'\n});\n\nvar resize_observer_border_box = /* @__PURE__ */ new ResizeObserverSingleton({\n\tbox: 'border-box'\n});\n\nvar resize_observer_device_pixel_content_box = /* @__PURE__ */ new ResizeObserverSingleton({\n\tbox: 'device-pixel-content-box'\n});\n\n/**\n * @param {Element} element\n * @param {'contentRect' | 'contentBoxSize' | 'borderBoxSize' | 'devicePixelContentBoxSize'} type\n * @param {(entry: keyof ResizeObserverEntry) => void} set\n */\nexport function bind_resize_observer(element, type, set) {\n\tvar observer =\n\t\ttype === 'contentRect' || type === 'contentBoxSize'\n\t\t\t? resize_observer_content_box\n\t\t\t: type === 'borderBoxSize'\n\t\t\t\t? resize_observer_border_box\n\t\t\t\t: resize_observer_device_pixel_content_box;\n\n\tvar unsub = observer.observe(element, /** @param {any} entry */ (entry) => set(entry[type]));\n\tteardown(unsub);\n}\n\n/**\n * @param {HTMLElement} element\n * @param {'clientWidth' | 'clientHeight' | 'offsetWidth' | 'offsetHeight'} type\n * @param {(size: number) => void} set\n */\nexport function bind_element_size(element, type, set) {\n\tvar unsub = resize_observer_border_box.observe(element, () => set(element[type]));\n\n\teffect(() => {\n\t\t// The update could contain reads which should be ignored\n\t\tuntrack(() => set(element[type]));\n\t\treturn unsub;\n\t});\n}\n", "import { STATE_SYMBOL } from '#client/constants';\nimport { effect, render_effect } from '../../../reactivity/effects.js';\nimport { untrack } from '../../../runtime.js';\nimport { queue_micro_task } from '../../task.js';\n\n/**\n * @param {any} bound_value\n * @param {Element} element_or_component\n * @returns {boolean}\n */\nfunction is_bound_this(bound_value, element_or_component) {\n\treturn (\n\t\tbound_value === element_or_component || bound_value?.[STATE_SYMBOL] === element_or_component\n\t);\n}\n\n/**\n * @param {any} element_or_component\n * @param {(value: unknown, ...parts: unknown[]) => void} update\n * @param {(...parts: unknown[]) => unknown} get_value\n * @param {() => unknown[]} [get_parts] Set if the this binding is used inside an each block,\n * \t\t\t\t\t\t\t\t\t\treturns all the parts of the each block context that are used in the expression\n * @returns {void}\n */\nexport function bind_this(element_or_component = {}, update, get_value, get_parts) {\n\teffect(() => {\n\t\t/** @type {unknown[]} */\n\t\tvar old_parts;\n\n\t\t/** @type {unknown[]} */\n\t\tvar parts;\n\n\t\trender_effect(() => {\n\t\t\told_parts = parts;\n\t\t\t// We only track changes to the parts, not the value itself to avoid unnecessary reruns.\n\t\t\tparts = get_parts?.() || [];\n\n\t\t\tuntrack(() => {\n\t\t\t\tif (element_or_component !== get_value(...parts)) {\n\t\t\t\t\tupdate(element_or_component, ...parts);\n\t\t\t\t\t// If this is an effect rerun (cause: each block context changes), then nullfiy the binding at\n\t\t\t\t\t// the previous position if it isn't already taken over by a different effect.\n\t\t\t\t\tif (old_parts && is_bound_this(get_value(...old_parts), element_or_component)) {\n\t\t\t\t\t\tupdate(null, ...old_parts);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\n\t\treturn () => {\n\t\t\t// We cannot use effects in the teardown phase, we we use a microtask instead.\n\t\t\tqueue_micro_task(() => {\n\t\t\t\tif (parts && is_bound_this(get_value(...parts), element_or_component)) {\n\t\t\t\t\tupdate(null, ...parts);\n\t\t\t\t}\n\t\t\t});\n\t\t};\n\t});\n\n\treturn element_or_component;\n}\n", "import { render_effect, teardown } from '../../../reactivity/effects.js';\nimport { listen } from './shared.js';\n\n/**\n * @param {'innerHTML' | 'textContent' | 'innerText'} property\n * @param {HTMLElement} element\n * @param {() => unknown} get\n * @param {(value: unknown) => void} set\n * @returns {void}\n */\nexport function bind_content_editable(property, element, get, set = get) {\n\telement.addEventListener('input', () => {\n\t\t// @ts-ignore\n\t\tset(element[property]);\n\t});\n\n\trender_effect(() => {\n\t\tvar value = get();\n\n\t\tif (element[property] !== value) {\n\t\t\tif (value == null) {\n\t\t\t\t// @ts-ignore\n\t\t\t\tvar non_null_value = element[property];\n\t\t\t\tset(non_null_value);\n\t\t\t} else {\n\t\t\t\t// @ts-ignore\n\t\t\t\telement[property] = value + '';\n\t\t\t}\n\t\t}\n\t});\n}\n\n/**\n * @param {string} property\n * @param {string} event_name\n * @param {Element} element\n * @param {(value: unknown) => void} set\n * @param {() => unknown} [get]\n * @returns {void}\n */\nexport function bind_property(property, event_name, element, set, get) {\n\tvar handler = () => {\n\t\t// @ts-ignore\n\t\tset(element[property]);\n\t};\n\n\telement.addEventListener(event_name, handler);\n\n\tif (get) {\n\t\trender_effect(() => {\n\t\t\t// @ts-ignore\n\t\t\telement[property] = get();\n\t\t});\n\t} else {\n\t\thandler();\n\t}\n\n\t// @ts-ignore\n\tif (element === document.body || element === window || element === document) {\n\t\tteardown(() => {\n\t\t\telement.removeEventListener(event_name, handler);\n\t\t});\n\t}\n}\n\n/**\n * @param {HTMLElement} element\n * @param {(value: unknown) => void} set\n * @returns {void}\n */\nexport function bind_focused(element, set) {\n\tlisten(element, ['focus', 'blur'], () => {\n\t\tset(element === document.activeElement);\n\t});\n}\n", "import { effect, render_effect, teardown } from '../../../reactivity/effects.js';\nimport { listen, without_reactive_context } from './shared.js';\n\n/**\n * @param {'x' | 'y'} type\n * @param {() => number} get\n * @param {(value: number) => void} set\n * @returns {void}\n */\nexport function bind_window_scroll(type, get, set = get) {\n\tvar is_scrolling_x = type === 'x';\n\n\tvar target_handler = () =>\n\t\twithout_reactive_context(() => {\n\t\t\tscrolling = true;\n\t\t\tclearTimeout(timeout);\n\t\t\ttimeout = setTimeout(clear, 100); // TODO use scrollend event if supported (or when supported everywhere?)\n\n\t\t\tset(window[is_scrolling_x ? 'scrollX' : 'scrollY']);\n\t\t});\n\n\taddEventListener('scroll', target_handler, {\n\t\tpassive: true\n\t});\n\n\tvar scrolling = false;\n\n\t/** @type {ReturnType<typeof setTimeout>} */\n\tvar timeout;\n\tvar clear = () => {\n\t\tscrolling = false;\n\t};\n\tvar first = true;\n\n\trender_effect(() => {\n\t\tvar latest_value = get();\n\t\t// Don't scroll to the initial value for accessibility reasons\n\t\tif (first) {\n\t\t\tfirst = false;\n\t\t} else if (!scrolling && latest_value != null) {\n\t\t\tscrolling = true;\n\t\t\tclearTimeout(timeout);\n\t\t\tif (is_scrolling_x) {\n\t\t\t\tscrollTo(latest_value, window.scrollY);\n\t\t\t} else {\n\t\t\t\tscrollTo(window.scrollX, latest_value);\n\t\t\t}\n\t\t\ttimeout = setTimeout(clear, 100);\n\t\t}\n\t});\n\n\t// Browsers don't fire the scroll event for the initial scroll position when scroll style isn't set to smooth\n\teffect(target_handler);\n\n\tteardown(() => {\n\t\tremoveEventListener('scroll', target_handler);\n\t});\n}\n\n/**\n * @param {'innerWidth' | 'innerHeight' | 'outerWidth' | 'outerHeight'} type\n * @param {(size: number) => void} set\n */\nexport function bind_window_size(type, set) {\n\tlisten(window, ['resize'], () => without_reactive_context(() => set(window[type])));\n}\n", "/** @import { ComponentContextLegacy } from '#client' */\nimport { run, run_all } from '../../../shared/utils.js';\nimport { component_context } from '../../context.js';\nimport { derived } from '../../reactivity/deriveds.js';\nimport { user_pre_effect, user_effect } from '../../reactivity/effects.js';\nimport { deep_read_state, get, untrack } from '../../runtime.js';\n\n/**\n * Legacy-mode only: Call `onMount` callbacks and set up `beforeUpdate`/`afterUpdate` effects\n * @param {boolean} [immutable]\n */\nexport function init(immutable = false) {\n\tconst context = /** @type {ComponentContextLegacy} */ (component_context);\n\n\tconst callbacks = context.l.u;\n\tif (!callbacks) return;\n\n\tlet props = () => deep_read_state(context.s);\n\n\tif (immutable) {\n\t\tlet version = 0;\n\t\tlet prev = /** @type {Record<string, any>} */ ({});\n\n\t\t// In legacy immutable mode, before/afterUpdate only fire if the object identity of a prop changes\n\t\tconst d = derived(() => {\n\t\t\tlet changed = false;\n\t\t\tconst props = context.s;\n\t\t\tfor (const key in props) {\n\t\t\t\tif (props[key] !== prev[key]) {\n\t\t\t\t\tprev[key] = props[key];\n\t\t\t\t\tchanged = true;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (changed) version++;\n\t\t\treturn version;\n\t\t});\n\n\t\tprops = () => get(d);\n\t}\n\n\t// beforeUpdate\n\tif (callbacks.b.length) {\n\t\tuser_pre_effect(() => {\n\t\t\tobserve_all(context, props);\n\t\t\trun_all(callbacks.b);\n\t\t});\n\t}\n\n\t// onMount (must run before afterUpdate)\n\tuser_effect(() => {\n\t\tconst fns = untrack(() => callbacks.m.map(run));\n\t\treturn () => {\n\t\t\tfor (const fn of fns) {\n\t\t\t\tif (typeof fn === 'function') {\n\t\t\t\t\tfn();\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\t});\n\n\t// afterUpdate\n\tif (callbacks.a.length) {\n\t\tuser_effect(() => {\n\t\t\tobserve_all(context, props);\n\t\t\trun_all(callbacks.a);\n\t\t});\n\t}\n}\n\n/**\n * Invoke the getter of all signals associated with a component\n * so they can be registered to the effect this function is called in.\n * @param {ComponentContextLegacy} context\n * @param {(() => void)} props\n */\nfunction observe_all(context, props) {\n\tif (context.l.s) {\n\t\tfor (const signal of context.l.s) get(signal);\n\t}\n\n\tprops();\n}\n", "import { set, source } from '../../reactivity/sources.js';\nimport { get } from '../../runtime.js';\nimport { is_array } from '../../../shared/utils.js';\n\n/**\n * Under some circumstances, imports may be reactive in legacy mode. In that case,\n * they should be using `reactive_import` as part of the transformation\n * @param {() => any} fn\n */\nexport function reactive_import(fn) {\n\tvar s = source(0);\n\n\treturn function () {\n\t\tif (arguments.length === 1) {\n\t\t\tset(s, get(s) + 1);\n\t\t\treturn arguments[0];\n\t\t} else {\n\t\t\tget(s);\n\t\t\treturn fn();\n\t\t}\n\t};\n}\n\n/**\n * @this {any}\n * @param {Record<string, unknown>} $$props\n * @param {Event} event\n * @returns {void}\n */\nexport function bubble_event($$props, event) {\n\tvar events = /** @type {Record<string, Function[] | Function>} */ ($$props.$$events)?.[\n\t\tevent.type\n\t];\n\n\tvar callbacks = is_array(events) ? events.slice() : events == null ? [] : [events];\n\n\tfor (var fn of callbacks) {\n\t\t// Preserve \"this\" context\n\t\tfn.call(this, event);\n\t}\n}\n\n/**\n * Used to simulate `$on` on a component instance when `compatibility.componentApi === 4`\n * @param {Record<string, any>} $$props\n * @param {string} event_name\n * @param {Function} event_callback\n */\nexport function add_legacy_event_listener($$props, event_name, event_callback) {\n\t$$props.$$events ||= {};\n\t$$props.$$events[event_name] ||= [];\n\t$$props.$$events[event_name].push(event_callback);\n}\n\n/**\n * Used to simulate `$set` on a component instance when `compatibility.componentApi === 4`.\n * Needs component accessors so that it can call the setter of the prop. Therefore doesn't\n * work for updating props in `$$props` or `$$restProps`.\n * @this {Record<string, any>}\n * @param {Record<string, any>} $$new_props\n */\nexport function update_legacy_props($$new_props) {\n\tfor (var key in $$new_props) {\n\t\tif (key in this) {\n\t\t\tthis[key] = $$new_props[key];\n\t\t}\n\t}\n}\n", "/** @import { ComponentContext, ComponentContextLegacy } from '#client' */\n/** @import { EventDispatcher } from './index.js' */\n/** @import { NotFunction } from './internal/types.js' */\nimport { untrack } from './internal/client/runtime.js';\nimport { is_array } from './internal/shared/utils.js';\nimport { user_effect } from './internal/client/index.js';\nimport * as e from './internal/client/errors.js';\nimport { lifecycle_outside_component } from './internal/shared/errors.js';\nimport { legacy_mode_flag } from './internal/flags/index.js';\nimport { component_context } from './internal/client/context.js';\nimport { DEV } from 'esm-env';\n\nif (DEV) {\n\t/**\n\t * @param {string} rune\n\t */\n\tfunction throw_rune_error(rune) {\n\t\tif (!(rune in globalThis)) {\n\t\t\t// TODO if people start adjusting the \"this can contain runes\" config through v-p-s more, adjust this message\n\t\t\t/** @type {any} */\n\t\t\tlet value; // let's hope noone modifies this global, but belts and braces\n\t\t\tObject.defineProperty(globalThis, rune, {\n\t\t\t\tconfigurable: true,\n\t\t\t\t// eslint-disable-next-line getter-return\n\t\t\t\tget: () => {\n\t\t\t\t\tif (value !== undefined) {\n\t\t\t\t\t\treturn value;\n\t\t\t\t\t}\n\n\t\t\t\t\te.rune_outside_svelte(rune);\n\t\t\t\t},\n\t\t\t\tset: (v) => {\n\t\t\t\t\tvalue = v;\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t}\n\n\tthrow_rune_error('$state');\n\tthrow_rune_error('$effect');\n\tthrow_rune_error('$derived');\n\tthrow_rune_error('$inspect');\n\tthrow_rune_error('$props');\n\tthrow_rune_error('$bindable');\n}\n\n/**\n * `onMount`, like [`$effect`](https://svelte.dev/docs/svelte/$effect), schedules a function to run as soon as the component has been mounted to the DOM.\n * Unlike `$effect`, the provided function only runs once.\n *\n * It must be called during the component's initialisation (but doesn't need to live _inside_ the component;\n * it can be called from an external module). If a function is returned _synchronously_ from `onMount`,\n * it will be called when the component is unmounted.\n *\n * `onMount` functions do not run during [server-side rendering](https://svelte.dev/docs/svelte/svelte-server#render).\n *\n * @template T\n * @param {() => NotFunction<T> | Promise<NotFunction<T>> | (() => any)} fn\n * @returns {void}\n */\nexport function onMount(fn) {\n\tif (component_context === null) {\n\t\tlifecycle_outside_component('onMount');\n\t}\n\n\tif (legacy_mode_flag && component_context.l !== null) {\n\t\tinit_update_callbacks(component_context).m.push(fn);\n\t} else {\n\t\tuser_effect(() => {\n\t\t\tconst cleanup = untrack(fn);\n\t\t\tif (typeof cleanup === 'function') return /** @type {() => void} */ (cleanup);\n\t\t});\n\t}\n}\n\n/**\n * Schedules a callback to run immediately before the component is unmounted.\n *\n * Out of `onMount`, `beforeUpdate`, `afterUpdate` and `onDestroy`, this is the\n * only one that runs inside a server-side component.\n *\n * @param {() => any} fn\n * @returns {void}\n */\nexport function onDestroy(fn) {\n\tif (component_context === null) {\n\t\tlifecycle_outside_component('onDestroy');\n\t}\n\n\tonMount(() => () => untrack(fn));\n}\n\n/**\n * @template [T=any]\n * @param {string} type\n * @param {T} [detail]\n * @param {any}params_0\n * @returns {CustomEvent<T>}\n */\nfunction create_custom_event(type, detail, { bubbles = false, cancelable = false } = {}) {\n\treturn new CustomEvent(type, { detail, bubbles, cancelable });\n}\n\n/**\n * Creates an event dispatcher that can be used to dispatch [component events](https://svelte.dev/docs/svelte/legacy-on#Component-events).\n * Event dispatchers are functions that can take two arguments: `name` and `detail`.\n *\n * Component events created with `createEventDispatcher` create a\n * [CustomEvent](https://developer.mozilla.org/en-US/docs/Web/API/CustomEvent).\n * These events do not [bubble](https://developer.mozilla.org/en-US/docs/Learn/JavaScript/Building_blocks/Events#Event_bubbling_and_capture).\n * The `detail` argument corresponds to the [CustomEvent.detail](https://developer.mozilla.org/en-US/docs/Web/API/CustomEvent/detail)\n * property and can contain any type of data.\n *\n * The event dispatcher can be typed to narrow the allowed event names and the type of the `detail` argument:\n * ```ts\n * const dispatch = createEventDispatcher<{\n *  loaded: null; // does not take a detail argument\n *  change: string; // takes a detail argument of type string, which is required\n *  optional: number | null; // takes an optional detail argument of type number\n * }>();\n * ```\n *\n * @deprecated Use callback props and/or the `$host()` rune instead — see [migration guide](https://svelte.dev/docs/svelte/v5-migration-guide#Event-changes-Component-events)\n * @template {Record<string, any>} [EventMap = any]\n * @returns {EventDispatcher<EventMap>}\n */\nexport function createEventDispatcher() {\n\tconst active_component_context = component_context;\n\tif (active_component_context === null) {\n\t\tlifecycle_outside_component('createEventDispatcher');\n\t}\n\n\treturn (type, detail, options) => {\n\t\tconst events = /** @type {Record<string, Function | Function[]>} */ (\n\t\t\tactive_component_context.s.$$events\n\t\t)?.[/** @type {any} */ (type)];\n\n\t\tif (events) {\n\t\t\tconst callbacks = is_array(events) ? events.slice() : [events];\n\t\t\t// TODO are there situations where events could be dispatched\n\t\t\t// in a server (non-DOM) environment?\n\t\t\tconst event = create_custom_event(/** @type {string} */ (type), detail, options);\n\t\t\tfor (const fn of callbacks) {\n\t\t\t\tfn.call(active_component_context.x, event);\n\t\t\t}\n\t\t\treturn !event.defaultPrevented;\n\t\t}\n\n\t\treturn true;\n\t};\n}\n\n// TODO mark beforeUpdate and afterUpdate as deprecated in Svelte 6\n\n/**\n * Schedules a callback to run immediately before the component is updated after any state change.\n *\n * The first time the callback runs will be before the initial `onMount`.\n *\n * In runes mode use `$effect.pre` instead.\n *\n * @deprecated Use [`$effect.pre`](https://svelte.dev/docs/svelte/$effect#$effect.pre) instead\n * @param {() => void} fn\n * @returns {void}\n */\nexport function beforeUpdate(fn) {\n\tif (component_context === null) {\n\t\tlifecycle_outside_component('beforeUpdate');\n\t}\n\n\tif (component_context.l === null) {\n\t\te.lifecycle_legacy_only('beforeUpdate');\n\t}\n\n\tinit_update_callbacks(component_context).b.push(fn);\n}\n\n/**\n * Schedules a callback to run immediately after the component has been updated.\n *\n * The first time the callback runs will be after the initial `onMount`.\n *\n * In runes mode use `$effect` instead.\n *\n * @deprecated Use [`$effect`](https://svelte.dev/docs/svelte/$effect) instead\n * @param {() => void} fn\n * @returns {void}\n */\nexport function afterUpdate(fn) {\n\tif (component_context === null) {\n\t\tlifecycle_outside_component('afterUpdate');\n\t}\n\n\tif (component_context.l === null) {\n\t\te.lifecycle_legacy_only('afterUpdate');\n\t}\n\n\tinit_update_callbacks(component_context).a.push(fn);\n}\n\n/**\n * Legacy-mode: Init callbacks object for onMount/beforeUpdate/afterUpdate\n * @param {ComponentContext} context\n */\nfunction init_update_callbacks(context) {\n\tvar l = /** @type {ComponentContextLegacy} */ (context).l;\n\treturn (l.u ??= { a: [], b: [], m: [] });\n}\n\nexport { flushSync } from './internal/client/runtime.js';\nexport { getContext, getAllContexts, hasContext, setContext } from './internal/client/context.js';\nexport { hydrate, mount, unmount } from './internal/client/render.js';\nexport { tick, untrack } from './internal/client/runtime.js';\nexport { createRawSnippet } from './internal/client/dom/blocks/snippet.js';\n", "/** @import { Readable } from './public' */\nimport { untrack } from '../index-client.js';\nimport { noop } from '../internal/shared/utils.js';\n\n/**\n * @template T\n * @param {Readable<T> | null | undefined} store\n * @param {(value: T) => void} run\n * @param {(value: T) => void} [invalidate]\n * @returns {() => void}\n */\nexport function subscribe_to_store(store, run, invalidate) {\n\tif (store == null) {\n\t\t// @ts-expect-error\n\t\trun(undefined);\n\n\t\t// @ts-expect-error\n\t\tif (invalidate) invalidate(undefined);\n\n\t\treturn noop;\n\t}\n\n\t// Svelte store takes a private second argument\n\t// StartStopNotifier could mutate state, and we want to silence the corresponding validation error\n\tconst unsub = untrack(() =>\n\t\tstore.subscribe(\n\t\t\trun,\n\t\t\t// @ts-expect-error\n\t\t\tinvalidate\n\t\t)\n\t);\n\n\t// Also support RxJS\n\t// @ts-expect-error TODO fix this in the types?\n\treturn unsub.unsubscribe ? () => unsub.unsubscribe() : unsub;\n}\n", "/** @import { Readable, StartStopNotifier, Subscriber, Unsubscriber, Updater, Writable } from '../public.js' */\n/** @import { Stores, StoresValues, SubscribeInvalidateTuple } from '../private.js' */\nimport { noop, run_all } from '../../internal/shared/utils.js';\nimport { safe_not_equal } from '../../internal/client/reactivity/equality.js';\nimport { subscribe_to_store } from '../utils.js';\n\n/**\n * @type {Array<SubscribeInvalidateTuple<any> | any>}\n */\nconst subscriber_queue = [];\n\n/**\n * Creates a `Readable` store that allows reading by subscription.\n *\n * @template T\n * @param {T} [value] initial value\n * @param {StartStopNotifier<T>} [start]\n * @returns {Readable<T>}\n */\nexport function readable(value, start) {\n\treturn {\n\t\tsubscribe: writable(value, start).subscribe\n\t};\n}\n\n/**\n * Create a `Writable` store that allows both updating and reading by subscription.\n *\n * @template T\n * @param {T} [value] initial value\n * @param {StartStopNotifier<T>} [start]\n * @returns {Writable<T>}\n */\nexport function writable(value, start = noop) {\n\t/** @type {Unsubscriber | null} */\n\tlet stop = null;\n\n\t/** @type {Set<SubscribeInvalidateTuple<T>>} */\n\tconst subscribers = new Set();\n\n\t/**\n\t * @param {T} new_value\n\t * @returns {void}\n\t */\n\tfunction set(new_value) {\n\t\tif (safe_not_equal(value, new_value)) {\n\t\t\tvalue = new_value;\n\t\t\tif (stop) {\n\t\t\t\t// store is ready\n\t\t\t\tconst run_queue = !subscriber_queue.length;\n\t\t\t\tfor (const subscriber of subscribers) {\n\t\t\t\t\tsubscriber[1]();\n\t\t\t\t\tsubscriber_queue.push(subscriber, value);\n\t\t\t\t}\n\t\t\t\tif (run_queue) {\n\t\t\t\t\tfor (let i = 0; i < subscriber_queue.length; i += 2) {\n\t\t\t\t\t\tsubscriber_queue[i][0](subscriber_queue[i + 1]);\n\t\t\t\t\t}\n\t\t\t\t\tsubscriber_queue.length = 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * @param {Updater<T>} fn\n\t * @returns {void}\n\t */\n\tfunction update(fn) {\n\t\tset(fn(/** @type {T} */ (value)));\n\t}\n\n\t/**\n\t * @param {Subscriber<T>} run\n\t * @param {() => void} [invalidate]\n\t * @returns {Unsubscriber}\n\t */\n\tfunction subscribe(run, invalidate = noop) {\n\t\t/** @type {SubscribeInvalidateTuple<T>} */\n\t\tconst subscriber = [run, invalidate];\n\t\tsubscribers.add(subscriber);\n\t\tif (subscribers.size === 1) {\n\t\t\tstop = start(set, update) || noop;\n\t\t}\n\t\trun(/** @type {T} */ (value));\n\t\treturn () => {\n\t\t\tsubscribers.delete(subscriber);\n\t\t\tif (subscribers.size === 0 && stop) {\n\t\t\t\tstop();\n\t\t\t\tstop = null;\n\t\t\t}\n\t\t};\n\t}\n\treturn { set, update, subscribe };\n}\n\n/**\n * Derived value store by synchronizing one or more readable stores and\n * applying an aggregation function over its input values.\n *\n * @template {Stores} S\n * @template T\n * @overload\n * @param {S} stores\n * @param {(values: StoresValues<S>, set: (value: T) => void, update: (fn: Updater<T>) => void) => Unsubscriber | void} fn\n * @param {T} [initial_value]\n * @returns {Readable<T>}\n */\n/**\n * Derived value store by synchronizing one or more readable stores and\n * applying an aggregation function over its input values.\n *\n * @template {Stores} S\n * @template T\n * @overload\n * @param {S} stores\n * @param {(values: StoresValues<S>) => T} fn\n * @param {T} [initial_value]\n * @returns {Readable<T>}\n */\n/**\n * @template {Stores} S\n * @template T\n * @param {S} stores\n * @param {Function} fn\n * @param {T} [initial_value]\n * @returns {Readable<T>}\n */\nexport function derived(stores, fn, initial_value) {\n\tconst single = !Array.isArray(stores);\n\t/** @type {Array<Readable<any>>} */\n\tconst stores_array = single ? [stores] : stores;\n\tif (!stores_array.every(Boolean)) {\n\t\tthrow new Error('derived() expects stores as input, got a falsy value');\n\t}\n\tconst auto = fn.length < 2;\n\treturn readable(initial_value, (set, update) => {\n\t\tlet started = false;\n\t\t/** @type {T[]} */\n\t\tconst values = [];\n\t\tlet pending = 0;\n\t\tlet cleanup = noop;\n\t\tconst sync = () => {\n\t\t\tif (pending) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tcleanup();\n\t\t\tconst result = fn(single ? values[0] : values, set, update);\n\t\t\tif (auto) {\n\t\t\t\tset(result);\n\t\t\t} else {\n\t\t\t\tcleanup = typeof result === 'function' ? result : noop;\n\t\t\t}\n\t\t};\n\t\tconst unsubscribers = stores_array.map((store, i) =>\n\t\t\tsubscribe_to_store(\n\t\t\t\tstore,\n\t\t\t\t(value) => {\n\t\t\t\t\tvalues[i] = value;\n\t\t\t\t\tpending &= ~(1 << i);\n\t\t\t\t\tif (started) {\n\t\t\t\t\t\tsync();\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t() => {\n\t\t\t\t\tpending |= 1 << i;\n\t\t\t\t}\n\t\t\t)\n\t\t);\n\t\tstarted = true;\n\t\tsync();\n\t\treturn function stop() {\n\t\t\trun_all(unsubscribers);\n\t\t\tcleanup();\n\t\t\t// We need to set this to false because callbacks can still happen despite having unsubscribed:\n\t\t\t// Callbacks might already be placed in the queue which doesn't know it should no longer\n\t\t\t// invoke this derived store.\n\t\t\tstarted = false;\n\t\t};\n\t});\n}\n\n/**\n * Takes a store and returns a new one derived from the old one that is readable.\n *\n * @template T\n * @param {Readable<T>} store  - store to make readonly\n * @returns {Readable<T>}\n */\nexport function readonly(store) {\n\treturn {\n\t\t// @ts-expect-error TODO i suspect the bind is unnecessary\n\t\tsubscribe: store.subscribe.bind(store)\n\t};\n}\n\n/**\n * Get the current value from a store by subscribing and immediately unsubscribing.\n *\n * @template T\n * @param {Readable<T>} store\n * @returns {T}\n */\nexport function get(store) {\n\tlet value;\n\tsubscribe_to_store(store, (_) => (value = _))();\n\t// @ts-expect-error\n\treturn value;\n}\n", "/** @import { StoreReferencesContainer } from '#client' */\n/** @import { Store } from '#shared' */\nimport { subscribe_to_store } from '../../../store/utils.js';\nimport { get as get_store } from '../../../store/shared/index.js';\nimport { define_property, noop } from '../../shared/utils.js';\nimport { get } from '../runtime.js';\nimport { teardown } from './effects.js';\nimport { mutable_source, set } from './sources.js';\n\n/**\n * Whether or not the prop currently being read is a store binding, as in\n * `<Child bind:x={$y} />`. If it is, we treat the prop as mutable even in\n * runes mode, and skip `binding_property_non_reactive` validation\n */\nlet is_store_binding = false;\n\nlet IS_UNMOUNTED = Symbol();\n\n/**\n * Gets the current value of a store. If the store isn't subscribed to yet, it will create a proxy\n * signal that will be updated when the store is. The store references container is needed to\n * track reassignments to stores and to track the correct component context.\n * @template V\n * @param {Store<V> | null | undefined} store\n * @param {string} store_name\n * @param {StoreReferencesContainer} stores\n * @returns {V}\n */\nexport function store_get(store, store_name, stores) {\n\tconst entry = (stores[store_name] ??= {\n\t\tstore: null,\n\t\tsource: mutable_source(undefined),\n\t\tunsubscribe: noop\n\t});\n\n\t// if the component that setup this is already unmounted we don't want to register a subscription\n\tif (entry.store !== store && !(IS_UNMOUNTED in stores)) {\n\t\tentry.unsubscribe();\n\t\tentry.store = store ?? null;\n\n\t\tif (store == null) {\n\t\t\tentry.source.v = undefined; // see synchronous callback comment below\n\t\t\tentry.unsubscribe = noop;\n\t\t} else {\n\t\t\tvar is_synchronous_callback = true;\n\n\t\t\tentry.unsubscribe = subscribe_to_store(store, (v) => {\n\t\t\t\tif (is_synchronous_callback) {\n\t\t\t\t\t// If the first updates to the store value (possibly multiple of them) are synchronously\n\t\t\t\t\t// inside a derived, we will hit the `state_unsafe_mutation` error if we `set` the value\n\t\t\t\t\tentry.source.v = v;\n\t\t\t\t} else {\n\t\t\t\t\tset(entry.source, v);\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tis_synchronous_callback = false;\n\t\t}\n\t}\n\n\t// if the component that setup this stores is already unmounted the source will be out of sync\n\t// so we just use the `get` for the stores, less performant but it avoids to create a memory leak\n\t// and it will keep the value consistent\n\tif (store && IS_UNMOUNTED in stores) {\n\t\treturn get_store(store);\n\t}\n\n\treturn get(entry.source);\n}\n\n/**\n * Unsubscribe from a store if it's not the same as the one in the store references container.\n * We need this in addition to `store_get` because someone could unsubscribe from a store but\n * then never subscribe to the new one (if any), causing the subscription to stay open wrongfully.\n * @param {Store<any> | null | undefined} store\n * @param {string} store_name\n * @param {StoreReferencesContainer} stores\n */\nexport function store_unsub(store, store_name, stores) {\n\t/** @type {StoreReferencesContainer[''] | undefined} */\n\tlet entry = stores[store_name];\n\n\tif (entry && entry.store !== store) {\n\t\t// Don't reset store yet, so that store_get above can resubscribe to new store if necessary\n\t\tentry.unsubscribe();\n\t\tentry.unsubscribe = noop;\n\t}\n\n\treturn store;\n}\n\n/**\n * Sets the new value of a store and returns that value.\n * @template V\n * @param {Store<V>} store\n * @param {V} value\n * @returns {V}\n */\nexport function store_set(store, value) {\n\tstore.set(value);\n\treturn value;\n}\n\n/**\n * @param {StoreReferencesContainer} stores\n * @param {string} store_name\n */\nexport function invalidate_store(stores, store_name) {\n\tvar entry = stores[store_name];\n\tif (entry.store !== null) {\n\t\tstore_set(entry.store, entry.source.v);\n\t}\n}\n\n/**\n * Unsubscribes from all auto-subscribed stores on destroy\n * @returns {[StoreReferencesContainer, ()=>void]}\n */\nexport function setup_stores() {\n\t/** @type {StoreReferencesContainer} */\n\tconst stores = {};\n\n\tfunction cleanup() {\n\t\tteardown(() => {\n\t\t\tfor (var store_name in stores) {\n\t\t\t\tconst ref = stores[store_name];\n\t\t\t\tref.unsubscribe();\n\t\t\t}\n\t\t\tdefine_property(stores, IS_UNMOUNTED, {\n\t\t\t\tenumerable: false,\n\t\t\t\tvalue: true\n\t\t\t});\n\t\t});\n\t}\n\n\treturn [stores, cleanup];\n}\n\n/**\n * Updates a store with a new value.\n * @param {Store<V>} store  the store to update\n * @param {any} expression  the expression that mutates the store\n * @param {V} new_value  the new store value\n * @template V\n */\nexport function store_mutate(store, expression, new_value) {\n\tstore.set(new_value);\n\treturn expression;\n}\n\n/**\n * @param {Store<number>} store\n * @param {number} store_value\n * @param {1 | -1} [d]\n * @returns {number}\n */\nexport function update_store(store, store_value, d = 1) {\n\tstore.set(store_value + d);\n\treturn store_value;\n}\n\n/**\n * @param {Store<number>} store\n * @param {number} store_value\n * @param {1 | -1} [d]\n * @returns {number}\n */\nexport function update_pre_store(store, store_value, d = 1) {\n\tconst value = store_value + d;\n\tstore.set(value);\n\treturn value;\n}\n\n/**\n * Called inside prop getters to communicate that the prop is a store binding\n */\nexport function mark_store_binding() {\n\tis_store_binding = true;\n}\n\n/**\n * Returns a tuple that indicates whether `fn()` reads a prop that is a store binding.\n * Used to prevent `binding_property_non_reactive` validation false positives and\n * ensure that these props are treated as mutable even in runes mode\n * @template T\n * @param {() => T} fn\n * @returns {[T, boolean]}\n */\nexport function capture_store_binding(fn) {\n\tvar previous_is_store_binding = is_store_binding;\n\n\ttry {\n\t\tis_store_binding = false;\n\t\treturn [fn(), is_store_binding];\n\t} finally {\n\t\tis_store_binding = previous_is_store_binding;\n\t}\n}\n", "/** @import { Derived, Source } from './types.js' */\nimport { DEV } from 'esm-env';\nimport {\n\tPROPS_IS_BINDABLE,\n\tPROPS_IS_IMMUTABLE,\n\tPROPS_IS_LAZY_INITIAL,\n\tPROPS_IS_RUNES,\n\tPROPS_IS_UPDATED\n} from '../../../constants.js';\nimport { get_descriptor, is_function } from '../../shared/utils.js';\nimport { mutable_source, set, source, update } from './sources.js';\nimport { derived, derived_safe_equal } from './deriveds.js';\nimport { get, captured_signals, untrack } from '../runtime.js';\nimport { safe_equals } from './equality.js';\nimport * as e from '../errors.js';\nimport { LEGACY_DERIVED_PROP, LEGACY_PROPS, STATE_SYMBOL } from '#client/constants';\nimport { proxy } from '../proxy.js';\nimport { capture_store_binding } from './store.js';\nimport { legacy_mode_flag } from '../../flags/index.js';\n\n/**\n * @param {((value?: number) => number)} fn\n * @param {1 | -1} [d]\n * @returns {number}\n */\nexport function update_prop(fn, d = 1) {\n\tconst value = fn();\n\tfn(value + d);\n\treturn value;\n}\n\n/**\n * @param {((value?: number) => number)} fn\n * @param {1 | -1} [d]\n * @returns {number}\n */\nexport function update_pre_prop(fn, d = 1) {\n\tconst value = fn() + d;\n\tfn(value);\n\treturn value;\n}\n\n/**\n * The proxy handler for rest props (i.e. `const { x, ...rest } = $props()`).\n * Is passed the full `$$props` object and excludes the named props.\n * @type {ProxyHandler<{ props: Record<string | symbol, unknown>, exclude: Array<string | symbol>, name?: string }>}}\n */\nconst rest_props_handler = {\n\tget(target, key) {\n\t\tif (target.exclude.includes(key)) return;\n\t\treturn target.props[key];\n\t},\n\tset(target, key) {\n\t\tif (DEV) {\n\t\t\t// TODO should this happen in prod too?\n\t\t\te.props_rest_readonly(`${target.name}.${String(key)}`);\n\t\t}\n\n\t\treturn false;\n\t},\n\tgetOwnPropertyDescriptor(target, key) {\n\t\tif (target.exclude.includes(key)) return;\n\t\tif (key in target.props) {\n\t\t\treturn {\n\t\t\t\tenumerable: true,\n\t\t\t\tconfigurable: true,\n\t\t\t\tvalue: target.props[key]\n\t\t\t};\n\t\t}\n\t},\n\thas(target, key) {\n\t\tif (target.exclude.includes(key)) return false;\n\t\treturn key in target.props;\n\t},\n\townKeys(target) {\n\t\treturn Reflect.ownKeys(target.props).filter((key) => !target.exclude.includes(key));\n\t}\n};\n\n/**\n * @param {Record<string, unknown>} props\n * @param {string[]} exclude\n * @param {string} [name]\n * @returns {Record<string, unknown>}\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function rest_props(props, exclude, name) {\n\treturn new Proxy(\n\t\tDEV ? { props, exclude, name, other: {}, to_proxy: [] } : { props, exclude },\n\t\trest_props_handler\n\t);\n}\n\n/**\n * The proxy handler for legacy $$restProps and $$props\n * @type {ProxyHandler<{ props: Record<string | symbol, unknown>, exclude: Array<string | symbol>, special: Record<string | symbol, (v?: unknown) => unknown>, version: Source<number> }>}}\n */\nconst legacy_rest_props_handler = {\n\tget(target, key) {\n\t\tif (target.exclude.includes(key)) return;\n\t\tget(target.version);\n\t\treturn key in target.special ? target.special[key]() : target.props[key];\n\t},\n\tset(target, key, value) {\n\t\tif (!(key in target.special)) {\n\t\t\t// Handle props that can temporarily get out of sync with the parent\n\t\t\t/** @type {Record<string, (v?: unknown) => unknown>} */\n\t\t\ttarget.special[key] = prop(\n\t\t\t\t{\n\t\t\t\t\tget [key]() {\n\t\t\t\t\t\treturn target.props[key];\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t/** @type {string} */ (key),\n\t\t\t\tPROPS_IS_UPDATED\n\t\t\t);\n\t\t}\n\n\t\ttarget.special[key](value);\n\t\tupdate(target.version); // $$props is coarse-grained: when $$props.x is updated, usages of $$props.y etc are also rerun\n\t\treturn true;\n\t},\n\tgetOwnPropertyDescriptor(target, key) {\n\t\tif (target.exclude.includes(key)) return;\n\t\tif (key in target.props) {\n\t\t\treturn {\n\t\t\t\tenumerable: true,\n\t\t\t\tconfigurable: true,\n\t\t\t\tvalue: target.props[key]\n\t\t\t};\n\t\t}\n\t},\n\tdeleteProperty(target, key) {\n\t\t// Svelte 4 allowed for deletions on $$restProps\n\t\tif (target.exclude.includes(key)) return true;\n\t\ttarget.exclude.push(key);\n\t\tupdate(target.version);\n\t\treturn true;\n\t},\n\thas(target, key) {\n\t\tif (target.exclude.includes(key)) return false;\n\t\treturn key in target.props;\n\t},\n\townKeys(target) {\n\t\treturn Reflect.ownKeys(target.props).filter((key) => !target.exclude.includes(key));\n\t}\n};\n\n/**\n * @param {Record<string, unknown>} props\n * @param {string[]} exclude\n * @returns {Record<string, unknown>}\n */\nexport function legacy_rest_props(props, exclude) {\n\treturn new Proxy({ props, exclude, special: {}, version: source(0) }, legacy_rest_props_handler);\n}\n\n/**\n * The proxy handler for spread props. Handles the incoming array of props\n * that looks like `() => { dynamic: props }, { static: prop }, ..` and wraps\n * them so that the whole thing is passed to the component as the `$$props` argument.\n * @template {Record<string | symbol, unknown>} T\n * @type {ProxyHandler<{ props: Array<T | (() => T)> }>}}\n */\nconst spread_props_handler = {\n\tget(target, key) {\n\t\tlet i = target.props.length;\n\t\twhile (i--) {\n\t\t\tlet p = target.props[i];\n\t\t\tif (is_function(p)) p = p();\n\t\t\tif (typeof p === 'object' && p !== null && key in p) return p[key];\n\t\t}\n\t},\n\tset(target, key, value) {\n\t\tlet i = target.props.length;\n\t\twhile (i--) {\n\t\t\tlet p = target.props[i];\n\t\t\tif (is_function(p)) p = p();\n\t\t\tconst desc = get_descriptor(p, key);\n\t\t\tif (desc && desc.set) {\n\t\t\t\tdesc.set(value);\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\t\treturn false;\n\t},\n\tgetOwnPropertyDescriptor(target, key) {\n\t\tlet i = target.props.length;\n\t\twhile (i--) {\n\t\t\tlet p = target.props[i];\n\t\t\tif (is_function(p)) p = p();\n\t\t\tif (typeof p === 'object' && p !== null && key in p) {\n\t\t\t\tconst descriptor = get_descriptor(p, key);\n\t\t\t\tif (descriptor && !descriptor.configurable) {\n\t\t\t\t\t// Prevent a \"Non-configurability Report Error\": The target is an array, it does\n\t\t\t\t\t// not actually contain this property. If it is now described as non-configurable,\n\t\t\t\t\t// the proxy throws a validation error. Setting it to true avoids that.\n\t\t\t\t\tdescriptor.configurable = true;\n\t\t\t\t}\n\t\t\t\treturn descriptor;\n\t\t\t}\n\t\t}\n\t},\n\thas(target, key) {\n\t\t// To prevent a false positive `is_entry_props` in the `prop` function\n\t\tif (key === STATE_SYMBOL || key === LEGACY_PROPS) return false;\n\n\t\tfor (let p of target.props) {\n\t\t\tif (is_function(p)) p = p();\n\t\t\tif (p != null && key in p) return true;\n\t\t}\n\n\t\treturn false;\n\t},\n\townKeys(target) {\n\t\t/** @type {Array<string | symbol>} */\n\t\tconst keys = [];\n\n\t\tfor (let p of target.props) {\n\t\t\tif (is_function(p)) p = p();\n\t\t\tif (!p) continue;\n\n\t\t\tfor (const key in p) {\n\t\t\t\tif (!keys.includes(key)) keys.push(key);\n\t\t\t}\n\n\t\t\tfor (const key of Object.getOwnPropertySymbols(p)) {\n\t\t\t\tif (!keys.includes(key)) keys.push(key);\n\t\t\t}\n\t\t}\n\n\t\treturn keys;\n\t}\n};\n\n/**\n * @param {Array<Record<string, unknown> | (() => Record<string, unknown>)>} props\n * @returns {any}\n */\nexport function spread_props(...props) {\n\treturn new Proxy({ props }, spread_props_handler);\n}\n\n/**\n * @param {Derived} current_value\n * @returns {boolean}\n */\nfunction has_destroyed_component_ctx(current_value) {\n\treturn current_value.ctx?.d ?? false;\n}\n\n/**\n * This function is responsible for synchronizing a possibly bound prop with the inner component state.\n * It is used whenever the compiler sees that the component writes to the prop, or when it has a default prop_value.\n * @template V\n * @param {Record<string, unknown>} props\n * @param {string} key\n * @param {number} flags\n * @param {V | (() => V)} [fallback]\n * @returns {(() => V | ((arg: V) => V) | ((arg: V, mutation: boolean) => V))}\n */\nexport function prop(props, key, flags, fallback) {\n\tvar immutable = (flags & PROPS_IS_IMMUTABLE) !== 0;\n\tvar runes = !legacy_mode_flag || (flags & PROPS_IS_RUNES) !== 0;\n\tvar bindable = (flags & PROPS_IS_BINDABLE) !== 0;\n\tvar lazy = (flags & PROPS_IS_LAZY_INITIAL) !== 0;\n\tvar is_store_sub = false;\n\tvar prop_value;\n\n\tif (bindable) {\n\t\t[prop_value, is_store_sub] = capture_store_binding(() => /** @type {V} */ (props[key]));\n\t} else {\n\t\tprop_value = /** @type {V} */ (props[key]);\n\t}\n\n\t// Can be the case when someone does `mount(Component, props)` with `let props = $state({...})`\n\t// or `createClassComponent(Component, props)`\n\tvar is_entry_props = STATE_SYMBOL in props || LEGACY_PROPS in props;\n\n\tvar setter =\n\t\t(bindable &&\n\t\t\t(get_descriptor(props, key)?.set ??\n\t\t\t\t(is_entry_props && key in props && ((v) => (props[key] = v))))) ||\n\t\tundefined;\n\n\tvar fallback_value = /** @type {V} */ (fallback);\n\tvar fallback_dirty = true;\n\tvar fallback_used = false;\n\n\tvar get_fallback = () => {\n\t\tfallback_used = true;\n\t\tif (fallback_dirty) {\n\t\t\tfallback_dirty = false;\n\t\t\tif (lazy) {\n\t\t\t\tfallback_value = untrack(/** @type {() => V} */ (fallback));\n\t\t\t} else {\n\t\t\t\tfallback_value = /** @type {V} */ (fallback);\n\t\t\t}\n\t\t}\n\n\t\treturn fallback_value;\n\t};\n\n\tif (prop_value === undefined && fallback !== undefined) {\n\t\tif (setter && runes) {\n\t\t\te.props_invalid_value(key);\n\t\t}\n\n\t\tprop_value = get_fallback();\n\t\tif (setter) setter(prop_value);\n\t}\n\n\t/** @type {() => V} */\n\tvar getter;\n\tif (runes) {\n\t\tgetter = () => {\n\t\t\tvar value = /** @type {V} */ (props[key]);\n\t\t\tif (value === undefined) return get_fallback();\n\t\t\tfallback_dirty = true;\n\t\t\tfallback_used = false;\n\t\t\treturn value;\n\t\t};\n\t} else {\n\t\t// Svelte 4 did not trigger updates when a primitive value was updated to the same value.\n\t\t// Replicate that behavior through using a derived\n\t\tvar derived_getter = (immutable ? derived : derived_safe_equal)(\n\t\t\t() => /** @type {V} */ (props[key])\n\t\t);\n\t\tderived_getter.f |= LEGACY_DERIVED_PROP;\n\t\tgetter = () => {\n\t\t\tvar value = get(derived_getter);\n\t\t\tif (value !== undefined) fallback_value = /** @type {V} */ (undefined);\n\t\t\treturn value === undefined ? fallback_value : value;\n\t\t};\n\t}\n\n\t// easy mode — prop is never written to\n\tif ((flags & PROPS_IS_UPDATED) === 0) {\n\t\treturn getter;\n\t}\n\n\t// intermediate mode — prop is written to, but the parent component had\n\t// `bind:foo` which means we can just call `$$props.foo = value` directly\n\tif (setter) {\n\t\tvar legacy_parent = props.$$legacy;\n\t\treturn function (/** @type {any} */ value, /** @type {boolean} */ mutation) {\n\t\t\tif (arguments.length > 0) {\n\t\t\t\t// We don't want to notify if the value was mutated and the parent is in runes mode.\n\t\t\t\t// In that case the state proxy (if it exists) should take care of the notification.\n\t\t\t\t// If the parent is not in runes mode, we need to notify on mutation, too, that the prop\n\t\t\t\t// has changed because the parent will not be able to detect the change otherwise.\n\t\t\t\tif (!runes || !mutation || legacy_parent || is_store_sub) {\n\t\t\t\t\t/** @type {Function} */ (setter)(mutation ? getter() : value);\n\t\t\t\t}\n\t\t\t\treturn value;\n\t\t\t} else {\n\t\t\t\treturn getter();\n\t\t\t}\n\t\t};\n\t}\n\n\t// hard mode. this is where it gets ugly — the value in the child should\n\t// synchronize with the parent, but it should also be possible to temporarily\n\t// set the value to something else locally.\n\tvar from_child = false;\n\tvar was_from_child = false;\n\n\t// The derived returns the current value. The underlying mutable\n\t// source is written to from various places to persist this value.\n\tvar inner_current_value = mutable_source(prop_value);\n\tvar current_value = derived(() => {\n\t\tvar parent_value = getter();\n\t\tvar child_value = get(inner_current_value);\n\n\t\tif (from_child) {\n\t\t\tfrom_child = false;\n\t\t\twas_from_child = true;\n\t\t\treturn child_value;\n\t\t}\n\n\t\twas_from_child = false;\n\t\treturn (inner_current_value.v = parent_value);\n\t});\n\n\t// Ensure we eagerly capture the initial value if it's bindable\n\tif (bindable) {\n\t\tget(current_value);\n\t}\n\n\tif (!immutable) current_value.equals = safe_equals;\n\n\treturn function (/** @type {any} */ value, /** @type {boolean} */ mutation) {\n\t\t// legacy nonsense — need to ensure the source is invalidated when necessary\n\t\t// also needed for when handling inspect logic so we can inspect the correct source signal\n\t\tif (captured_signals !== null) {\n\t\t\t// set this so that we don't reset to the parent value if `d`\n\t\t\t// is invalidated because of `invalidate_inner_signals` (rather\n\t\t\t// than because the parent or child value changed)\n\t\t\tfrom_child = was_from_child;\n\t\t\t// invoke getters so that signals are picked up by `invalidate_inner_signals`\n\t\t\tgetter();\n\t\t\tget(inner_current_value);\n\t\t}\n\n\t\tif (arguments.length > 0) {\n\t\t\tconst new_value = mutation ? get(current_value) : runes && bindable ? proxy(value) : value;\n\n\t\t\tif (!current_value.equals(new_value)) {\n\t\t\t\tfrom_child = true;\n\t\t\t\tset(inner_current_value, new_value);\n\t\t\t\t// To ensure the fallback value is consistent when used with proxies, we\n\t\t\t\t// update the local fallback_value, but only if the fallback is actively used\n\t\t\t\tif (fallback_used && fallback_value !== undefined) {\n\t\t\t\t\tfallback_value = new_value;\n\t\t\t\t}\n\n\t\t\t\tif (has_destroyed_component_ctx(current_value)) {\n\t\t\t\t\treturn value;\n\t\t\t\t}\n\n\t\t\t\tuntrack(() => get(current_value)); // force a synchronisation immediately\n\t\t\t}\n\n\t\t\treturn value;\n\t\t}\n\n\t\tif (has_destroyed_component_ctx(current_value)) {\n\t\t\treturn current_value.v;\n\t\t}\n\n\t\treturn get(current_value);\n\t};\n}\n", "/** @import { Effect, TemplateNode, } from '#client' */\n\nimport { BOUNDARY_EFFECT, EFFECT_TRANSPARENT } from '#client/constants';\nimport { component_context, set_component_context } from '../../context.js';\nimport { block, branch, destroy_effect, pause_effect } from '../../reactivity/effects.js';\nimport {\n\tactive_effect,\n\tactive_reaction,\n\thandle_error,\n\tset_active_effect,\n\tset_active_reaction,\n\treset_is_throwing_error\n} from '../../runtime.js';\nimport {\n\thydrate_next,\n\thydrate_node,\n\thydrating,\n\tnext,\n\tremove_nodes,\n\tset_hydrate_node\n} from '../hydration.js';\nimport { queue_micro_task } from '../task.js';\n\n/**\n * @param {Effect} boundary\n * @param {() => void} fn\n */\nfunction with_boundary(boundary, fn) {\n\tvar previous_effect = active_effect;\n\tvar previous_reaction = active_reaction;\n\tvar previous_ctx = component_context;\n\n\tset_active_effect(boundary);\n\tset_active_reaction(boundary);\n\tset_component_context(boundary.ctx);\n\n\ttry {\n\t\tfn();\n\t} finally {\n\t\tset_active_effect(previous_effect);\n\t\tset_active_reaction(previous_reaction);\n\t\tset_component_context(previous_ctx);\n\t}\n}\n\n/**\n * @param {TemplateNode} node\n * @param {{\n * \t onerror?: (error: unknown, reset: () => void) => void,\n *   failed?: (anchor: Node, error: () => unknown, reset: () => () => void) => void\n * }} props\n * @param {((anchor: Node) => void)} boundary_fn\n * @returns {void}\n */\nexport function boundary(node, props, boundary_fn) {\n\tvar anchor = node;\n\n\t/** @type {Effect} */\n\tvar boundary_effect;\n\n\tblock(() => {\n\t\tvar boundary = /** @type {Effect} */ (active_effect);\n\t\tvar hydrate_open = hydrate_node;\n\t\tvar is_creating_fallback = false;\n\n\t\t// We re-use the effect's fn property to avoid allocation of an additional field\n\t\tboundary.fn = (/** @type {unknown}} */ error) => {\n\t\t\tvar onerror = props.onerror;\n\t\t\tlet failed = props.failed;\n\n\t\t\t// If we have nothing to capture the error, or if we hit an error while\n\t\t\t// rendering the fallback, re-throw for another boundary to handle\n\t\t\tif ((!onerror && !failed) || is_creating_fallback) {\n\t\t\t\tthrow error;\n\t\t\t}\n\n\t\t\tvar reset = () => {\n\t\t\t\tpause_effect(boundary_effect);\n\n\t\t\t\twith_boundary(boundary, () => {\n\t\t\t\t\tis_creating_fallback = false;\n\t\t\t\t\tboundary_effect = branch(() => boundary_fn(anchor));\n\t\t\t\t\treset_is_throwing_error();\n\t\t\t\t});\n\t\t\t};\n\n\t\t\tonerror?.(error, reset);\n\n\t\t\tif (boundary_effect) {\n\t\t\t\tdestroy_effect(boundary_effect);\n\t\t\t} else if (hydrating) {\n\t\t\t\tset_hydrate_node(hydrate_open);\n\t\t\t\tnext();\n\t\t\t\tset_hydrate_node(remove_nodes());\n\t\t\t}\n\n\t\t\tif (failed) {\n\t\t\t\t// Render the `failed` snippet in a microtask\n\t\t\t\tqueue_micro_task(() => {\n\t\t\t\t\twith_boundary(boundary, () => {\n\t\t\t\t\t\tis_creating_fallback = true;\n\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tboundary_effect = branch(() => {\n\t\t\t\t\t\t\t\tfailed(\n\t\t\t\t\t\t\t\t\tanchor,\n\t\t\t\t\t\t\t\t\t() => error,\n\t\t\t\t\t\t\t\t\t() => reset\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\thandle_error(error, boundary, null, boundary.ctx);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treset_is_throwing_error();\n\t\t\t\t\t\tis_creating_fallback = false;\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t}\n\t\t};\n\n\t\tif (hydrating) {\n\t\t\thydrate_next();\n\t\t}\n\n\t\tboundary_effect = branch(() => boundary_fn(anchor));\n\t\treset_is_throwing_error();\n\t}, EFFECT_TRANSPARENT | BOUNDARY_EFFECT);\n\n\tif (hydrating) {\n\t\tanchor = hydrate_node;\n\t}\n}\n", "import { dev_current_component_function } from './context.js';\nimport { is_array } from '../shared/utils.js';\nimport * as e from './errors.js';\nimport { FILENAME } from '../../constants.js';\nimport { render_effect } from './reactivity/effects.js';\nimport * as w from './warnings.js';\nimport { capture_store_binding } from './reactivity/store.js';\n\n/**\n * @param {() => any} collection\n * @param {(item: any, index: number) => string} key_fn\n * @returns {void}\n */\nexport function validate_each_keys(collection, key_fn) {\n\trender_effect(() => {\n\t\tconst keys = new Map();\n\t\tconst maybe_array = collection();\n\t\tconst array = is_array(maybe_array)\n\t\t\t? maybe_array\n\t\t\t: maybe_array == null\n\t\t\t\t? []\n\t\t\t\t: Array.from(maybe_array);\n\t\tconst length = array.length;\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\tconst key = key_fn(array[i], i);\n\t\t\tif (keys.has(key)) {\n\t\t\t\tconst a = String(keys.get(key));\n\t\t\t\tconst b = String(i);\n\n\t\t\t\t/** @type {string | null} */\n\t\t\t\tlet k = String(key);\n\t\t\t\tif (k.startsWith('[object ')) k = null;\n\n\t\t\t\te.each_key_duplicate(a, b, k);\n\t\t\t}\n\t\t\tkeys.set(key, i);\n\t\t}\n\t});\n}\n\n/**\n * @param {string} binding\n * @param {() => Record<string, any>} get_object\n * @param {() => string} get_property\n * @param {number} line\n * @param {number} column\n */\nexport function validate_binding(binding, get_object, get_property, line, column) {\n\tvar warned = false;\n\n\tvar filename = dev_current_component_function?.[FILENAME];\n\n\trender_effect(() => {\n\t\tif (warned) return;\n\n\t\tvar [object, is_store_sub] = capture_store_binding(get_object);\n\n\t\tif (is_store_sub) return;\n\n\t\tvar property = get_property();\n\n\t\tvar ran = false;\n\n\t\t// by making the (possibly false, but it would be an extreme edge case) assumption\n\t\t// that a getter has a corresponding setter, we can determine if a property is\n\t\t// reactive by seeing if this effect has dependencies\n\t\tvar effect = render_effect(() => {\n\t\t\tif (ran) return;\n\n\t\t\t// eslint-disable-next-line @typescript-eslint/no-unused-expressions\n\t\t\tobject[property];\n\t\t});\n\n\t\tran = true;\n\n\t\tif (effect.deps === null) {\n\t\t\tvar location = `${filename}:${line}:${column}`;\n\t\t\tw.binding_property_non_reactive(binding, location);\n\n\t\t\twarned = true;\n\t\t}\n\t});\n}\n", "import { createClassComponent } from '../../../../legacy/legacy-client.js';\nimport { effect_root, render_effect } from '../../reactivity/effects.js';\nimport { append } from '../template.js';\nimport { define_property, get_descriptor, object_keys } from '../../../shared/utils.js';\n\n/**\n * @typedef {Object} CustomElementPropDefinition\n * @property {string} [attribute]\n * @property {boolean} [reflect]\n * @property {'String'|'Boolean'|'Number'|'Array'|'Object'} [type]\n */\n\n/** @type {any} */\nlet SvelteElement;\n\nif (typeof HTMLElement === 'function') {\n\tSvelteElement = class extends HTMLElement {\n\t\t/** The Svelte component constructor */\n\t\t$$ctor;\n\t\t/** Slots */\n\t\t$$s;\n\t\t/** @type {any} The Svelte component instance */\n\t\t$$c;\n\t\t/** Whether or not the custom element is connected */\n\t\t$$cn = false;\n\t\t/** @type {Record<string, any>} Component props data */\n\t\t$$d = {};\n\t\t/** `true` if currently in the process of reflecting component props back to attributes */\n\t\t$$r = false;\n\t\t/** @type {Record<string, CustomElementPropDefinition>} Props definition (name, reflected, type etc) */\n\t\t$$p_d = {};\n\t\t/** @type {Record<string, EventListenerOrEventListenerObject[]>} Event listeners */\n\t\t$$l = {};\n\t\t/** @type {Map<EventListenerOrEventListenerObject, Function>} Event listener unsubscribe functions */\n\t\t$$l_u = new Map();\n\t\t/** @type {any} The managed render effect for reflecting attributes */\n\t\t$$me;\n\n\t\t/**\n\t\t * @param {*} $$componentCtor\n\t\t * @param {*} $$slots\n\t\t * @param {*} use_shadow_dom\n\t\t */\n\t\tconstructor($$componentCtor, $$slots, use_shadow_dom) {\n\t\t\tsuper();\n\t\t\tthis.$$ctor = $$componentCtor;\n\t\t\tthis.$$s = $$slots;\n\t\t\tif (use_shadow_dom) {\n\t\t\t\tthis.attachShadow({ mode: 'open' });\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * @param {string} type\n\t\t * @param {EventListenerOrEventListenerObject} listener\n\t\t * @param {boolean | AddEventListenerOptions} [options]\n\t\t */\n\t\taddEventListener(type, listener, options) {\n\t\t\t// We can't determine upfront if the event is a custom event or not, so we have to\n\t\t\t// listen to both. If someone uses a custom event with the same name as a regular\n\t\t\t// browser event, this fires twice - we can't avoid that.\n\t\t\tthis.$$l[type] = this.$$l[type] || [];\n\t\t\tthis.$$l[type].push(listener);\n\t\t\tif (this.$$c) {\n\t\t\t\tconst unsub = this.$$c.$on(type, listener);\n\t\t\t\tthis.$$l_u.set(listener, unsub);\n\t\t\t}\n\t\t\tsuper.addEventListener(type, listener, options);\n\t\t}\n\n\t\t/**\n\t\t * @param {string} type\n\t\t * @param {EventListenerOrEventListenerObject} listener\n\t\t * @param {boolean | AddEventListenerOptions} [options]\n\t\t */\n\t\tremoveEventListener(type, listener, options) {\n\t\t\tsuper.removeEventListener(type, listener, options);\n\t\t\tif (this.$$c) {\n\t\t\t\tconst unsub = this.$$l_u.get(listener);\n\t\t\t\tif (unsub) {\n\t\t\t\t\tunsub();\n\t\t\t\t\tthis.$$l_u.delete(listener);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tasync connectedCallback() {\n\t\t\tthis.$$cn = true;\n\t\t\tif (!this.$$c) {\n\t\t\t\t// We wait one tick to let possible child slot elements be created/mounted\n\t\t\t\tawait Promise.resolve();\n\t\t\t\tif (!this.$$cn || this.$$c) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t/** @param {string} name */\n\t\t\t\tfunction create_slot(name) {\n\t\t\t\t\t/**\n\t\t\t\t\t * @param {Element} anchor\n\t\t\t\t\t */\n\t\t\t\t\treturn (anchor) => {\n\t\t\t\t\t\tconst slot = document.createElement('slot');\n\t\t\t\t\t\tif (name !== 'default') slot.name = name;\n\n\t\t\t\t\t\tappend(anchor, slot);\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\t/** @type {Record<string, any>} */\n\t\t\t\tconst $$slots = {};\n\t\t\t\tconst existing_slots = get_custom_elements_slots(this);\n\t\t\t\tfor (const name of this.$$s) {\n\t\t\t\t\tif (name in existing_slots) {\n\t\t\t\t\t\tif (name === 'default' && !this.$$d.children) {\n\t\t\t\t\t\t\tthis.$$d.children = create_slot(name);\n\t\t\t\t\t\t\t$$slots.default = true;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t$$slots[name] = create_slot(name);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tfor (const attribute of this.attributes) {\n\t\t\t\t\t// this.$$data takes precedence over this.attributes\n\t\t\t\t\tconst name = this.$$g_p(attribute.name);\n\t\t\t\t\tif (!(name in this.$$d)) {\n\t\t\t\t\t\tthis.$$d[name] = get_custom_element_value(name, attribute.value, this.$$p_d, 'toProp');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// Port over props that were set programmatically before ce was initialized\n\t\t\t\tfor (const key in this.$$p_d) {\n\t\t\t\t\t// @ts-expect-error\n\t\t\t\t\tif (!(key in this.$$d) && this[key] !== undefined) {\n\t\t\t\t\t\t// @ts-expect-error\n\t\t\t\t\t\tthis.$$d[key] = this[key]; // don't transform, these were set through JavaScript\n\t\t\t\t\t\t// @ts-expect-error\n\t\t\t\t\t\tdelete this[key]; // remove the property that shadows the getter/setter\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.$$c = createClassComponent({\n\t\t\t\t\tcomponent: this.$$ctor,\n\t\t\t\t\ttarget: this.shadowRoot || this,\n\t\t\t\t\tprops: {\n\t\t\t\t\t\t...this.$$d,\n\t\t\t\t\t\t$$slots,\n\t\t\t\t\t\t$$host: this\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\t// Reflect component props as attributes\n\t\t\t\tthis.$$me = effect_root(() => {\n\t\t\t\t\trender_effect(() => {\n\t\t\t\t\t\tthis.$$r = true;\n\t\t\t\t\t\tfor (const key of object_keys(this.$$c)) {\n\t\t\t\t\t\t\tif (!this.$$p_d[key]?.reflect) continue;\n\t\t\t\t\t\t\tthis.$$d[key] = this.$$c[key];\n\t\t\t\t\t\t\tconst attribute_value = get_custom_element_value(\n\t\t\t\t\t\t\t\tkey,\n\t\t\t\t\t\t\t\tthis.$$d[key],\n\t\t\t\t\t\t\t\tthis.$$p_d,\n\t\t\t\t\t\t\t\t'toAttribute'\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\tif (attribute_value == null) {\n\t\t\t\t\t\t\t\tthis.removeAttribute(this.$$p_d[key].attribute || key);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthis.setAttribute(this.$$p_d[key].attribute || key, attribute_value);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.$$r = false;\n\t\t\t\t\t});\n\t\t\t\t});\n\n\t\t\t\tfor (const type in this.$$l) {\n\t\t\t\t\tfor (const listener of this.$$l[type]) {\n\t\t\t\t\t\tconst unsub = this.$$c.$on(type, listener);\n\t\t\t\t\t\tthis.$$l_u.set(listener, unsub);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.$$l = {};\n\t\t\t}\n\t\t}\n\n\t\t// We don't need this when working within Svelte code, but for compatibility of people using this outside of Svelte\n\t\t// and setting attributes through setAttribute etc, this is helpful\n\n\t\t/**\n\t\t * @param {string} attr\n\t\t * @param {string} _oldValue\n\t\t * @param {string} newValue\n\t\t */\n\t\tattributeChangedCallback(attr, _oldValue, newValue) {\n\t\t\tif (this.$$r) return;\n\t\t\tattr = this.$$g_p(attr);\n\t\t\tthis.$$d[attr] = get_custom_element_value(attr, newValue, this.$$p_d, 'toProp');\n\t\t\tthis.$$c?.$set({ [attr]: this.$$d[attr] });\n\t\t}\n\n\t\tdisconnectedCallback() {\n\t\t\tthis.$$cn = false;\n\t\t\t// In a microtask, because this could be a move within the DOM\n\t\t\tPromise.resolve().then(() => {\n\t\t\t\tif (!this.$$cn && this.$$c) {\n\t\t\t\t\tthis.$$c.$destroy();\n\t\t\t\t\tthis.$$me();\n\t\t\t\t\tthis.$$c = undefined;\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\n\t\t/**\n\t\t * @param {string} attribute_name\n\t\t */\n\t\t$$g_p(attribute_name) {\n\t\t\treturn (\n\t\t\t\tobject_keys(this.$$p_d).find(\n\t\t\t\t\t(key) =>\n\t\t\t\t\t\tthis.$$p_d[key].attribute === attribute_name ||\n\t\t\t\t\t\t(!this.$$p_d[key].attribute && key.toLowerCase() === attribute_name)\n\t\t\t\t) || attribute_name\n\t\t\t);\n\t\t}\n\t};\n}\n\n/**\n * @param {string} prop\n * @param {any} value\n * @param {Record<string, CustomElementPropDefinition>} props_definition\n * @param {'toAttribute' | 'toProp'} [transform]\n */\nfunction get_custom_element_value(prop, value, props_definition, transform) {\n\tconst type = props_definition[prop]?.type;\n\tvalue = type === 'Boolean' && typeof value !== 'boolean' ? value != null : value;\n\tif (!transform || !props_definition[prop]) {\n\t\treturn value;\n\t} else if (transform === 'toAttribute') {\n\t\tswitch (type) {\n\t\t\tcase 'Object':\n\t\t\tcase 'Array':\n\t\t\t\treturn value == null ? null : JSON.stringify(value);\n\t\t\tcase 'Boolean':\n\t\t\t\treturn value ? '' : null;\n\t\t\tcase 'Number':\n\t\t\t\treturn value == null ? null : value;\n\t\t\tdefault:\n\t\t\t\treturn value;\n\t\t}\n\t} else {\n\t\tswitch (type) {\n\t\t\tcase 'Object':\n\t\t\tcase 'Array':\n\t\t\t\treturn value && JSON.parse(value);\n\t\t\tcase 'Boolean':\n\t\t\t\treturn value; // conversion already handled above\n\t\t\tcase 'Number':\n\t\t\t\treturn value != null ? +value : value;\n\t\t\tdefault:\n\t\t\t\treturn value;\n\t\t}\n\t}\n}\n\n/**\n * @param {HTMLElement} element\n */\nfunction get_custom_elements_slots(element) {\n\t/** @type {Record<string, true>} */\n\tconst result = {};\n\telement.childNodes.forEach((node) => {\n\t\tresult[/** @type {Element} node */ (node).slot || 'default'] = true;\n\t});\n\treturn result;\n}\n\n/**\n * @internal\n *\n * Turn a Svelte component into a custom element.\n * @param {any} Component  A Svelte component function\n * @param {Record<string, CustomElementPropDefinition>} props_definition  The props to observe\n * @param {string[]} slots  The slots to create\n * @param {string[]} exports  Explicitly exported values, other than props\n * @param {boolean} use_shadow_dom  Whether to use shadow DOM\n * @param {(ce: new () => HTMLElement) => new () => HTMLElement} [extend]\n */\nexport function create_custom_element(\n\tComponent,\n\tprops_definition,\n\tslots,\n\texports,\n\tuse_shadow_dom,\n\textend\n) {\n\tlet Class = class extends SvelteElement {\n\t\tconstructor() {\n\t\t\tsuper(Component, slots, use_shadow_dom);\n\t\t\tthis.$$p_d = props_definition;\n\t\t}\n\t\tstatic get observedAttributes() {\n\t\t\treturn object_keys(props_definition).map((key) =>\n\t\t\t\t(props_definition[key].attribute || key).toLowerCase()\n\t\t\t);\n\t\t}\n\t};\n\tobject_keys(props_definition).forEach((prop) => {\n\t\tdefine_property(Class.prototype, prop, {\n\t\t\tget() {\n\t\t\t\treturn this.$$c && prop in this.$$c ? this.$$c[prop] : this.$$d[prop];\n\t\t\t},\n\t\t\tset(value) {\n\t\t\t\tvalue = get_custom_element_value(prop, value, props_definition);\n\t\t\t\tthis.$$d[prop] = value;\n\t\t\t\tvar component = this.$$c;\n\n\t\t\t\tif (component) {\n\t\t\t\t\t// // If the instance has an accessor, use that instead\n\t\t\t\t\tvar setter = get_descriptor(component, prop)?.get;\n\n\t\t\t\t\tif (setter) {\n\t\t\t\t\t\tcomponent[prop] = value;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tcomponent.$set({ [prop]: value });\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t});\n\texports.forEach((property) => {\n\t\tdefine_property(Class.prototype, property, {\n\t\t\tget() {\n\t\t\t\treturn this.$$c?.[property];\n\t\t\t}\n\t\t});\n\t});\n\tif (extend) {\n\t\t// @ts-expect-error - assigning here is fine\n\t\tClass = extend(Class);\n\t}\n\tComponent.element = /** @type {any} */ Class;\n\treturn Class;\n}\n", "import { STATE_SYMBOL } from '#client/constants';\nimport { snapshot } from '../../shared/clone.js';\nimport * as w from '../warnings.js';\nimport { untrack } from '../runtime.js';\n\n/**\n * @param {string} method\n * @param  {...any} objects\n */\nexport function log_if_contains_state(method, ...objects) {\n\tuntrack(() => {\n\t\ttry {\n\t\t\tlet has_state = false;\n\t\t\tconst transformed = [];\n\n\t\t\tfor (const obj of objects) {\n\t\t\t\tif (obj && typeof obj === 'object' && STATE_SYMBOL in obj) {\n\t\t\t\t\ttransformed.push(snapshot(obj, true));\n\t\t\t\t\thas_state = true;\n\t\t\t\t} else {\n\t\t\t\t\ttransformed.push(obj);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (has_state) {\n\t\t\t\tw.console_log_state(method);\n\n\t\t\t\t// eslint-disable-next-line no-console\n\t\t\t\tconsole.log('%c[snapshot]', 'color: grey', ...transformed);\n\t\t\t}\n\t\t} catch {}\n\t});\n\n\treturn objects;\n}\n", "/** @import { Action, ActionReturn } from '../action/public' */\n/** @import { Attachment } from './public' */\nimport { noop, render_effect } from 'svelte/internal/client';\nimport { ATTACHMENT_KEY } from '../constants.js';\nimport { untrack } from '../index-client.js';\nimport { teardown } from '../internal/client/reactivity/effects.js';\n\n/**\n * Creates an object key that will be recognised as an attachment when the object is spread onto an element,\n * as a programmatic alternative to using `{@attach ...}`. This can be useful for library authors, though\n * is generally not needed when building an app.\n *\n * ```svelte\n * <script>\n * \timport { createAttachmentKey } from 'svelte/attachments';\n *\n * \tconst props = {\n * \t\tclass: 'cool',\n * \t\tonclick: () => alert('clicked'),\n * \t\t[createAttachmentKey()]: (node) => {\n * \t\t\tnode.textContent = 'attached!';\n * \t\t}\n * \t};\n * </script>\n *\n * <button {...props}>click me</button>\n * ```\n * @since 5.29\n */\nexport function createAttachmentKey() {\n\treturn Symbol(ATTACHMENT_KEY);\n}\n\n/**\n * Converts an [action](https://svelte.dev/docs/svelte/use) into an [attachment](https://svelte.dev/docs/svelte/@attach) keeping the same behavior.\n * It's useful if you want to start using attachments on components but you have actions provided by a library.\n *\n * Note that the second argument, if provided, must be a function that _returns_ the argument to the\n * action function, not the argument itself.\n *\n * ```svelte\n * <!-- with an action -->\n * <div use:foo={bar}>...</div>\n *\n * <!-- with an attachment -->\n * <div {@attach fromAction(foo, () => bar)}>...</div>\n * ```\n * @template {EventTarget} E\n * @template {unknown} T\n * @overload\n * @param {Action<E, T> | ((element: E, arg: T) => void | ActionReturn<T>)} action The action function\n * @param {() => T} fn A function that returns the argument for the action\n * @returns {Attachment<E>}\n */\n/**\n * Converts an [action](https://svelte.dev/docs/svelte/use) into an [attachment](https://svelte.dev/docs/svelte/@attach) keeping the same behavior.\n * It's useful if you want to start using attachments on components but you have actions provided by a library.\n *\n * Note that the second argument, if provided, must be a function that _returns_ the argument to the\n * action function, not the argument itself.\n *\n * ```svelte\n * <!-- with an action -->\n * <div use:foo={bar}>...</div>\n *\n * <!-- with an attachment -->\n * <div {@attach fromAction(foo, () => bar)}>...</div>\n * ```\n * @template {EventTarget} E\n * @overload\n * @param {Action<E, void> | ((element: E) => void | ActionReturn<void>)} action The action function\n * @returns {Attachment<E>}\n */\n/**\n * Converts an [action](https://svelte.dev/docs/svelte/use) into an [attachment](https://svelte.dev/docs/svelte/@attach) keeping the same behavior.\n * It's useful if you want to start using attachments on components but you have actions provided by a library.\n *\n * Note that the second argument, if provided, must be a function that _returns_ the argument to the\n * action function, not the argument itself.\n *\n * ```svelte\n * <!-- with an action -->\n * <div use:foo={bar}>...</div>\n *\n * <!-- with an attachment -->\n * <div {@attach fromAction(foo, () => bar)}>...</div>\n * ```\n *\n * @template {EventTarget} E\n * @template {unknown} T\n * @param {Action<E, T> | ((element: E, arg: T) => void | ActionReturn<T>)} action The action function\n * @param {() => T} fn A function that returns the argument for the action\n * @returns {Attachment<E>}\n * @since 5.32\n */\nexport function fromAction(action, fn = /** @type {() => T} */ (noop)) {\n\treturn (element) => {\n\t\tconst { update, destroy } = untrack(() => action(element, fn()) ?? {});\n\n\t\tif (update) {\n\t\t\tvar ran = false;\n\t\t\trender_effect(() => {\n\t\t\t\tconst arg = fn();\n\t\t\t\tif (ran) update(arg);\n\t\t\t});\n\t\t\tran = true;\n\t\t}\n\n\t\tif (destroy) {\n\t\t\tteardown(destroy);\n\t\t}\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWA,SAAS,QAAQ,GAAG,GAAG,UAAU,UAAU;AAC1C,MAAI,MAAM,GAAG;AACZ,IAAE;AAAA,MAAuB;AAAA;AAAA,MAAiC,kBAAkB,QAAQ;AAAA,IAAE;AAAA,EACvF;AAEA,SAAO;AACR;AAQO,SAAS,OAAO,QAAQ,UAAU,OAAO,UAAU;AACzD,SAAO;AAAA,IACL,OAAO,QAAQ,IAAI;AAAA,IACpB,QAAQ,MAAM,OAAO,QAAQ,CAAC;AAAA,IAC9B;AAAA,IACA;AAAA,EACD;AACD;AAQO,SAAS,WAAW,QAAQ,UAAU,OAAO,UAAU;AAC7D,SAAO;AAAA,IACL,wCAAqB;AAAA,IACtB,QAAQ,MAAM,OAAO,QAAQ,CAAC;AAAA,IAC9B;AAAA,IACA;AAAA,EACD;AACD;AAQO,SAAS,UAAU,QAAQ,UAAU,OAAO,UAAU;AAC5D,SAAO;AAAA,IACL,wCAAqB;AAAA,IACtB,QAAQ,MAAM,OAAO,QAAQ,CAAC;AAAA,IAC9B;AAAA,IACA;AAAA,EACD;AACD;AAQO,SAAS,eAAe,QAAQ,UAAU,OAAO,UAAU;AACjE,SAAO;AAAA,IACL,wCAAqB;AAAA,IACtB,QAAQ,MAAM,OAAO,QAAQ,CAAC;AAAA,IAC9B;AAAA,IACA;AAAA,EACD;AACD;;;AC5EA,IAAI,aAAa,oBAAI,IAAI;AAMlB,SAAS,eAAeA,OAAM,OAAO;AAC3C,MAAI,SAAS,WAAW,IAAIA,KAAI;AAEhC,MAAI,CAAC,QAAQ;AACZ,aAAS,oBAAI,IAAI;AACjB,eAAW,IAAIA,OAAM,MAAM;AAAA,EAC5B;AAEA,SAAO,IAAI,KAAK;AACjB;AAKO,SAAS,eAAeA,OAAM;AACpC,MAAI,SAAS,WAAW,IAAIA,KAAI;AAChC,MAAI,CAAC,OAAQ;AAEb,aAAW,SAAS,QAAQ;AAC3B,UAAM,OAAO;AAAA,EACd;AAEA,aAAW,OAAOA,KAAI;AACvB;;;ACpBO,SAAS,cAAc,IAAI,UAAU,WAAW;AACtD,SAAO,IAAyB,SAAS;AACxC,UAAM,MAAM,GAAG,GAAG,IAAI;AAEtB,QAAI,OAAO,YAAY,MAAM,IAAI,aAAa,KAAK,IAAI,aAAa;AACpE,qBAAiB,MAAM,UAAU,SAAS;AAE1C,WAAO;AAAA,EACR;AACD;AAOA,SAAS,gBAAgBC,UAAS,UAAU,UAAU;AAErD,EAAAA,SAAQ,gBAAgB;AAAA,IACvB,KAAK,EAAE,MAAM,UAAU,MAAM,SAAS,CAAC,GAAG,QAAQ,SAAS,CAAC,EAAE;AAAA,EAC/D;AAEA,MAAI,SAAS,CAAC,GAAG;AAChB,qBAAiBA,SAAQ,YAAY,UAAU,SAAS,CAAC,CAAC;AAAA,EAC3D;AACD;AAOA,SAAS,iBAAiB,MAAM,UAAU,WAAW;AACpD,MAAI,IAAI;AACR,MAAI,QAAQ;AAEZ,SAAO,QAAQ,IAAI,UAAU,QAAQ;AACpC,QAAI,aAAa,KAAK,aAAa,GAAG;AACrC,UAAIC;AAAA;AAAA,QAAkC;AAAA;AACtC,UAAIA,SAAQ,SAAS,mBAAmBA,SAAQ,SAAS,qBAAsB,UAAS;AAAA,eAC/EA,SAAQ,KAAK,CAAC,MAAM,cAAe,UAAS;AAAA,IACtD;AAEA,QAAI,UAAU,KAAK,KAAK,aAAa,GAAG;AACvC;AAAA;AAAA,QAAwC;AAAA,QAAO;AAAA,QAAU,UAAU,GAAG;AAAA,MAAC;AAAA,IACxE;AAEA,WAAO,KAAK;AAAA,EACb;AACD;;;AC7CO,SAAS,IAAI,UAAU,YAAY;AAKzC,WAAS,QAAQ,QAAQ,OAAO;AAC/B,QAAI,WAAW,CAAC;AAGhB,QAAIC;AAEJ,QAAI,MAAM;AAEV,UAAM,MAAM;AACX,YAAMC,UAAS,WAAW;AAC1B,YAAMC,aAAY,IAAID,OAAM;AAE5B,UAAID,SAAQ;AAEX,iBAAS,KAAK,SAAU,QAAO,SAAS,CAAC;AACzC,uBAAeA,OAAM;AAAA,MACtB;AAEA,MAAAA,UAAS,OAAO,MAAM;AAErB,YAAI,IAAK,kBAAiB,KAAK;AAG/B,eAAO;AAAA,UACN;AAAA,UACA,OAAO;AAAA;AAAA,YAEN,aAAa,IAAIE,WAAU,QAAQ,KAAK,IAAIA,WAAU,QAAQ,KAAK;AAAA,UACpE;AAAA,QACD;AAEA,YAAI,IAAK,kBAAiB,IAAI;AAAA,MAC/B,CAAC;AAAA,IACF,GAAG,kBAAkB;AAErB,UAAM;AAEN,QAAI,WAAW;AACd,eAAS;AAAA,IACV;AAEA,WAAO;AAAA,EACR;AAGA,UAAQ,QAAQ,IAAI,SAAS,QAAQ;AAGrC,UAAQ,GAAG,IAAI;AAAA;AAAA,IAEd;AAAA;AAAA;AAAA;AAAA,IAIA,QAAQ,OAAO,QAAQ;AAAA,EACxB;AAEA,SAAO;AACR;;;AC9DO,SAAS,2BAA2B,OAAO;AAflD;AAgBC,QAAMC,cAAY,8CAAmB;AACrC,QAAM,UAAS,oDAAmB,MAAnB,mBAAsB;AAErC,SAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQN,UAAU,CAACC,OAAM,MAAM,QAAQ,MAAM,WAAW;AAC/C,YAAM,OAAO,KAAK,CAAC;AACnB,UAAI,kBAAkB,OAAO,IAAI,KAAK,CAAC,QAAQ;AAC9C,eAAO;AAAA,MACR;AAGA,UAAI,QAAQ;AAEZ,eAAS,IAAI,GAAG,IAAI,KAAK,SAAS,GAAG,KAAK;AACzC,gBAAQ,MAAM,KAAK,CAAC,CAAC;AACrB,YAAI,EAAC,+BAAQ,gBAAe;AAC3B,iBAAO;AAAA,QACR;AAAA,MACD;AAEA,YAAM,WAAW,kBAAkB,GAAGD,WAAU,QAAQ,CAAC,IAAI,IAAI,IAAI,MAAM,EAAE;AAE7E,MAAE,2BAA2B,MAAM,UAAUC,OAAM,OAAO,QAAQ,CAAC;AAEnE,aAAO;AAAA,IACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,SAAS,CAAC,KAAK,iBAAiB,UAAU;AAtD5C,UAAAC;AAuDG,UAAI,CAAC,kBAAkB,OAAO,GAAG,KAAK,YAAUA,MAAA,MAAM,MAAN,gBAAAA,IAAU,gBAAe;AACxE,QAAE;AAAA,UACDF,WAAU,QAAQ;AAAA,UAClB;AAAA,UACA,gBAAgB,QAAQ;AAAA,UACxB,OAAO,QAAQ;AAAA,QAChB;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;AAMA,SAAS,kBAAkB,OAAO,WAAW;AAvE7C;AA0EC,QAAM,iBAAiB,gBAAgB,SAAS,gBAAgB;AAChE,SACC,CAAC,GAAC,oBAAe,OAAO,SAAS,MAA/B,mBAAkC,QACnC,kBAAkB,aAAa,SAChC,EAAE,aAAa;AAEjB;;;AC3EO,SAAS,aAAa,QAAQ;AACpC,MAAI,QAAQ;AACX,IAAE,0BAA0B,OAAO,QAAQ,KAAK,eAAe,OAAO,IAAI;AAAA,EAC3E;AACD;AAEO,SAAS,aAAa;AAX7B;AAYC,QAAMG,cAAY,8CAAmB;AAGrC,WAAS,MAAM,QAAQ;AACtB,IAAE,sBAAsB,QAAQA,WAAU,QAAQ,CAAC;AAAA,EACpD;AAEA,SAAO;AAAA,IACN,UAAU,MAAM,MAAM,YAAY;AAAA,IAClC,KAAK,MAAM,MAAM,UAAU;AAAA,IAC3B,MAAM,MAAM,MAAM,WAAW;AAAA,EAC9B;AACD;;;ACfO,SAAS,QAAQ,WAAW,YAAY,QAAQ,KAAK;AAC3D,kBAAgB,UAAU;AAE1B,MAAI,UAAU;AAEd,iBAAe,MAAM;AAEpB,QAAI,QAAQ;AAMZ,QAAI;AACH,cAAQ,UAAU;AAAA,IACnB,SAAS,OAAO;AAEf,cAAQ,MAAM,KAAK;AAAA,IACpB;AAEA,QAAI,UAAU,eAAe;AAC5B,gBAAU,UAAU,SAAS,UAAU,GAAG,SAAS,OAAO,IAAI,CAAC;AAAA,IAChE;AAEA,cAAU;AAAA,EACX,CAAC;AACF;;;AC9BO,SAAS,sBAAsB,WAAW,MAAM;AACtD,MAAI,OAAO,WAAW,YAAY,EAAE,kBAAkB,OAAO;AAC5D,8BAA0B;AAAA,EAC3B;AACA,WAAS,OAAO,MAAM;AACrB,QAAI,OAAO,QAAQ,YAAY;AAC9B,gCAA0B;AAAA,IAC3B;AAAA,EACD;AACD;;;ACSA,IAAM,UAAU;AAChB,IAAM,OAAO;AACb,IAAM,QAAQ;AAWP,SAAS,YAAY,MAAM,WAAW,YAAY,SAAS,UAAU;AApC5E;AAqCC,MAAI,WAAW;AACd,iBAAa;AAAA,EACd;AAEA,MAAI,SAAS;AACb,MAAI,QAAQ,SAAS;AACrB,MAAI,2BAA2B;AAG/B,MAAI,qBAAqB,gBAAM,8CAAmB,WAAW;AAG7D,MAAI,QAAQ;AAGZ,MAAI;AAGJ,MAAI;AAGJ,MAAI;AAEJ,MAAI,gBAAgB,QAAQ,SAAS;AAAA;AAAA,IAAkC;AAAA,EAAU;AACjF,MAAI,gBAAgB,QAAQ,SAAS,gBAAgB,MAAS;AAC9D,MAAI,WAAW;AAMf,WAASC,QAAOC,QAAO,SAAS;AAC/B,eAAW;AAEX,QAAI,SAAS;AACZ,wBAAkBC,OAAM;AACxB,0BAAoBA,OAAM;AAC1B,4BAAsB,wBAAwB;AAC9C,UAAI,aAAK,oCAAmC,kBAAkB;AAAA,IAC/D;AAEA,QAAI;AACH,UAAID,WAAU,WAAW,YAAY;AACpC,YAAI,eAAgB,eAAc,cAAc;AAAA,YAC3C,kBAAiB,OAAO,MAAM,WAAW,MAAM,CAAC;AAAA,MACtD;AAEA,UAAIA,WAAU,QAAQ,SAAS;AAC9B,YAAI,YAAa,eAAc,WAAW;AAAA,YACrC,eAAc,OAAO,MAAM,QAAQ,QAAQ,YAAY,CAAC;AAAA,MAC9D;AAEA,UAAIA,WAAU,SAAS,UAAU;AAChC,YAAI,aAAc,eAAc,YAAY;AAAA,YACvC,gBAAe,OAAO,MAAM,SAAS,QAAQ,YAAY,CAAC;AAAA,MAChE;AAEA,UAAIA,WAAU,WAAW,gBAAgB;AACxC,qBAAa,gBAAgB,MAAO,iBAAiB,IAAK;AAAA,MAC3D;AAEA,UAAIA,WAAU,QAAQ,aAAa;AAClC,qBAAa,aAAa,MAAO,cAAc,IAAK;AAAA,MACrD;AAEA,UAAIA,WAAU,SAAS,cAAc;AACpC,qBAAa,cAAc,MAAO,eAAe,IAAK;AAAA,MACvD;AAAA,IACD,UAAE;AACD,UAAI,SAAS;AACZ,YAAI,aAAK,oCAAmC,IAAI;AAChD,8BAAsB,IAAI;AAC1B,4BAAoB,IAAI;AACxB,0BAAkB,IAAI;AAItB,kBAAU;AAAA,MACX;AAAA,IACD;AAAA,EACD;AAEA,MAAIC,UAAS,MAAM,MAAM;AACxB,QAAI,WAAW,QAAQ,UAAU,GAAI;AAIrC,QAAI,WAAW,aAAa,WAAW,KAAK,OAAO,OAAO,SAAS;AAEnE,QAAI,UAAU;AAEb,eAAS,aAAa;AAEtB,uBAAiB,MAAM;AACvB,oBAAc,KAAK;AACnB,iBAAW;AAAA,IACZ;AAEA,QAAI,WAAW,KAAK,GAAG;AACtB,UAAI,UAAU;AAEd,iBAAW;AAEX,cAAQ;AAAA,QACP,CAAC,UAAU;AACV,cAAI,YAAY,MAAO;AAGvB,uBAAa,cAAc,KAAK;AAChC,UAAAF,QAAO,MAAM,IAAI;AAAA,QAClB;AAAA,QACA,CAAC,UAAU;AACV,cAAI,YAAY,MAAO;AAGvB,uBAAa,cAAc,KAAK;AAChC,UAAAA,QAAO,OAAO,IAAI;AAClB,cAAI,CAAC,UAAU;AAEd,kBAAM,aAAa;AAAA,UACpB;AAAA,QACD;AAAA,MACD;AAEA,UAAI,WAAW;AACd,YAAI,YAAY;AACf,2BAAiB,OAAO,MAAM,WAAW,MAAM,CAAC;AAAA,QACjD;AAAA,MACD,OAAO;AAGN,yBAAiB,MAAM;AACtB,cAAI,CAAC,SAAU,CAAAA,QAAO,SAAS,IAAI;AAAA,QACpC,CAAC;AAAA,MACF;AAAA,IACD,OAAO;AACN,mBAAa,cAAc,KAAK;AAChC,MAAAA,QAAO,MAAM,KAAK;AAAA,IACnB;AAEA,QAAI,UAAU;AAEb,oBAAc,IAAI;AAAA,IACnB;AAGA,WAAO,MAAO,QAAQ;AAAA,EACvB,CAAC;AAED,MAAI,WAAW;AACd,aAAS;AAAA,EACV;AACD;;;ACzKO,SAAS,SAAS,MAAM,IAAI,CAAC,YAAY,aAAa,IAAI,CAAC,GAAG,CAAC,GAAG;AACxE,MAAI,aAAa,eAAe,GAAG;AAClC,iBAAa;AAAA,EACd;AAEA,MAAI,SAAS;AAGb,MAAI,oBAAoB;AAGxB,MAAI,mBAAmB;AAGvB,MAAI,YAAY;AAEhB,MAAI,QAAQ,aAAa,IAAI,qBAAqB;AAElD,MAAI,aAAa;AAEjB,QAAM,aAAa,CAC8CG,KAChE,OAAO,SACH;AACJ,iBAAa;AACb,kBAAc,MAAMA,GAAE;AAAA,EACvB;AAEA,QAAM,gBAAgB,CACS,eAC2CA,QACrE;AACJ,QAAI,eAAe,YAAY,eAAgB;AAG/C,QAAI,WAAW;AAEf,QAAI,aAAa,kBAAkB,IAAI;AACtC,UAAI,eAAe,GAAG;AACrB,cAAM,OAAO,2BAA2B,MAAM;AAE9C,YAAI,SAAS,iBAAiB;AAC7B,0BAAgB;AAAA,QACjB,WAAW,SAAS,sBAAsB;AACzC,0BAAgB;AAAA,QACjB,OAAO;AACN,0BAAgB,SAAS,KAAK,UAAU,CAAC,CAAC;AAC1C,cAAI,kBAAkB,eAAe;AAGpC,4BAAgB,YAAY,WAAW;AAAA,UACxC;AAAA,QACD;AAAA,MACD;AACA,YAAM,UAAU,gBAAgB;AAEhC,UAAI,CAAC,CAAC,cAAc,SAAS;AAG5B,iBAAS,aAAa;AAEtB,yBAAiB,MAAM;AACvB,sBAAc,KAAK;AACnB,mBAAW;AACX,wBAAgB;AAAA,MACjB;AAAA,IACD;AAEA,QAAI,WAAW;AACd,UAAI,mBAAmB;AACtB,sBAAc,iBAAiB;AAAA,MAChC,WAAWA,KAAI;AACd,4BAAoB,OAAO,MAAMA,IAAG,MAAM,CAAC;AAAA,MAC5C;AAEA,UAAI,kBAAkB;AACrB,qBAAa,kBAAkB,MAAM;AACpC,6BAAmB;AAAA,QACpB,CAAC;AAAA,MACF;AAAA,IACD,OAAO;AACN,UAAI,kBAAkB;AACrB,sBAAc,gBAAgB;AAAA,MAC/B,WAAWA,KAAI;AACd,2BAAmB,OAAO,MAAMA,IAAG,QAAQ,CAAC,aAAa,GAAG,aAAa,CAAC,CAAC;AAAA,MAC5E;AAEA,UAAI,mBAAmB;AACtB,qBAAa,mBAAmB,MAAM;AACrC,8BAAoB;AAAA,QACrB,CAAC;AAAA,MACF;AAAA,IACD;AAEA,QAAI,UAAU;AAEb,oBAAc,IAAI;AAAA,IACnB;AAAA,EACD;AAEA,QAAM,MAAM;AACX,iBAAa;AACb,OAAG,UAAU;AACb,QAAI,CAAC,YAAY;AAChB,oBAAc,MAAM,IAAI;AAAA,IACzB;AAAA,EACD,GAAG,KAAK;AAER,MAAI,WAAW;AACd,aAAS;AAAA,EACV;AACD;;;ACrHO,SAAS,UAAU,MAAM,SAAS,WAAW;AACnD,MAAI,WAAW;AACd,iBAAa;AAAA,EACd;AAEA,MAAI,SAAS;AAGb,MAAI,MAAM;AAGV,MAAIC;AAEJ,MAAI,UAAU,SAAS,IAAI,YAAY;AAEvC,QAAM,MAAM;AACX,QAAI,QAAQ,KAAM,MAAM,QAAQ,CAAE,GAAG;AACpC,UAAIA,SAAQ;AACX,qBAAaA,OAAM;AAAA,MACpB;AAEA,MAAAA,UAAS,OAAO,MAAM,UAAU,MAAM,CAAC;AAAA,IACxC;AAAA,EACD,CAAC;AAED,MAAI,WAAW;AACd,aAAS;AAAA,EACV;AACD;;;AChCO,SAAS,UAAUC,UAAS,YAAY;AAC9C,MAAI,WAAW;AACd;AAAA;AAAA,MAA8C,gBAAgBA,QAAO;AAAA,IAAE;AAAA,EACxE;AAEA,gBAAc,MAAM;AACnB,QAAI,SAAS,WAAW;AAExB,aAAS,OAAO,QAAQ;AACvB,UAAI,QAAQ,OAAO,GAAG;AAEtB,UAAI,OAAO;AACV,QAAAA,SAAQ,MAAM,YAAY,KAAK,KAAK;AAAA,MACrC,OAAO;AACN,QAAAA,SAAQ,MAAM,eAAe,GAAG;AAAA,MACjC;AAAA,IACD;AAAA,EACD,CAAC;AAED,WAAS,MAAM;AACd,IAAAA,SAAQ,OAAO;AAAA,EAChB,CAAC;AACF;;;ACeO,IAAI,oBAAoB;AAGxB,SAAS,sBAAsB,MAAM;AAC3C,sBAAoB;AACrB;AAMO,SAAS,MAAM,GAAG,GAAG;AAC3B,SAAO;AACR;AAUA,SAAS,cAAcC,QAAO,OAAO,mBAAmB,WAAW;AAElE,MAAI,cAAc,CAAC;AACnB,MAAI,SAAS,MAAM;AAEnB,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,mBAAe,MAAM,CAAC,EAAE,GAAG,aAAa,IAAI;AAAA,EAC7C;AAEA,MAAI,gBAAgB,SAAS,KAAK,YAAY,WAAW,KAAK,sBAAsB;AAGpF,MAAI,eAAe;AAClB,QAAI;AAAA;AAAA;AAAA,MACqB,kBAAmB;AAAA;AAE5C,uBAAmB,WAAW;AAC9B,gBAAY;AAAA;AAAA,MAA+B;AAAA,IAAkB;AAC7D,cAAU,MAAM;AAChB,SAAKA,QAAO,MAAM,CAAC,EAAE,MAAM,MAAM,SAAS,CAAC,EAAE,IAAI;AAAA,EAClD;AAEA,sBAAoB,aAAa,MAAM;AACtC,aAASC,KAAI,GAAGA,KAAI,QAAQA,MAAK;AAChC,UAAI,OAAO,MAAMA,EAAC;AAClB,UAAI,CAAC,eAAe;AACnB,kBAAU,OAAO,KAAK,CAAC;AACvB,aAAKD,QAAO,KAAK,MAAM,KAAK,IAAI;AAAA,MACjC;AACA,qBAAe,KAAK,GAAG,CAAC,aAAa;AAAA,IACtC;AAAA,EACD,CAAC;AACF;AAYO,SAAS,KAAK,MAAM,OAAO,gBAAgB,SAAS,WAAW,cAAc,MAAM;AACzF,MAAI,SAAS;AAGb,MAAIA,SAAQ,EAAE,OAAO,OAAO,oBAAI,IAAI,GAAG,OAAO,KAAK;AAEnD,MAAI,iBAAiB,QAAQ,wBAAwB;AAErD,MAAI,eAAe;AAClB,QAAI;AAAA;AAAA,MAAsC;AAAA;AAE1C,aAAS,YACN;AAAA;AAAA,MAAgD,gBAAgB,WAAW;AAAA,IAAE,IAC7E,YAAY,YAAY,YAAY,CAAC;AAAA,EACzC;AAEA,MAAI,WAAW;AACd,iBAAa;AAAA,EACd;AAGA,MAAIE,YAAW;AAEf,MAAI,YAAY;AAKhB,MAAI,aAAa,mBAAmB,MAAM;AACzC,QAAI,aAAa,eAAe;AAEhC,WAAO,SAAS,UAAU,IAAI,aAAa,cAAc,OAAO,CAAC,IAAI,WAAW,UAAU;AAAA,EAC3F,CAAC;AAED,QAAM,MAAM;AACX,QAAI,QAAQ,IAAI,UAAU;AAC1B,QAAI,SAAS,MAAM;AAEnB,QAAI,aAAa,WAAW,GAAG;AAG9B;AAAA,IACD;AACA,gBAAY,WAAW;AAGvB,QAAI,WAAW;AAEf,QAAI,WAAW;AACd,UAAI,UAAU,2BAA2B,MAAM,MAAM;AAErD,UAAI,aAAa,WAAW,IAAI;AAE/B,iBAAS,aAAa;AAEtB,yBAAiB,MAAM;AACvB,sBAAc,KAAK;AACnB,mBAAW;AAAA,MACZ;AAAA,IACD;AAGA,QAAI,WAAW;AAEd,UAAI,OAAO;AAGX,UAAI;AAEJ,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,YACC,aAAa,aAAa;AAAA,QACF,aAAc,SAAS,eAC9C;AAGD;AAAA,UAAiC;AACjC,qBAAW;AACX,wBAAc,KAAK;AACnB;AAAA,QACD;AAEA,YAAI,QAAQ,MAAM,CAAC;AACnB,YAAI,MAAM,QAAQ,OAAO,CAAC;AAC1B,eAAO;AAAA,UACN;AAAA,UACAF;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACD;AACA,QAAAA,OAAM,MAAM,IAAI,KAAK,IAAI;AAEzB,eAAO;AAAA,MACR;AAGA,UAAI,SAAS,GAAG;AACf,yBAAiB,aAAa,CAAC;AAAA,MAChC;AAAA,IACD;AAEA,QAAI,CAAC,WAAW;AACf,gBAAU,OAAOA,QAAO,QAAQ,WAAW,OAAO,SAAS,cAAc;AAAA,IAC1E;AAEA,QAAI,gBAAgB,MAAM;AACzB,UAAI,WAAW,GAAG;AACjB,YAAIE,WAAU;AACb,wBAAcA,SAAQ;AAAA,QACvB,OAAO;AACN,UAAAA,YAAW,OAAO,MAAM,YAAY,MAAM,CAAC;AAAA,QAC5C;AAAA,MACD,WAAWA,cAAa,MAAM;AAC7B,qBAAaA,WAAU,MAAM;AAC5B,UAAAA,YAAW;AAAA,QACZ,CAAC;AAAA,MACF;AAAA,IACD;AAEA,QAAI,UAAU;AAEb,oBAAc,IAAI;AAAA,IACnB;AAQA,QAAI,UAAU;AAAA,EACf,CAAC;AAED,MAAI,WAAW;AACd,aAAS;AAAA,EACV;AACD;AAcA,SAAS,UAAU,OAAOF,QAAO,QAAQ,WAAW,OAAO,SAAS,gBAAgB;AA9QpF;AA+QC,MAAI,eAAe,QAAQ,sBAAsB;AACjD,MAAI,iBAAiB,SAAS,qBAAqB,0BAA0B;AAE7E,MAAI,SAAS,MAAM;AACnB,MAAI,QAAQA,OAAM;AAClB,MAAI,QAAQA,OAAM;AAClB,MAAI,UAAU;AAGd,MAAI;AAGJ,MAAI,OAAO;AAGX,MAAI;AAGJ,MAAI,UAAU,CAAC;AAGf,MAAI,UAAU,CAAC;AAGf,MAAI;AAGJ,MAAI;AAGJ,MAAI;AAGJ,MAAI;AAEJ,MAAI,aAAa;AAChB,SAAK,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAC/B,cAAQ,MAAM,CAAC;AACf,YAAM,QAAQ,OAAO,CAAC;AACtB,aAAO,MAAM,IAAI,GAAG;AAEpB,UAAI,SAAS,QAAW;AACvB,mBAAK,MAAL,mBAAQ;AACR,SAAC,4BAAe,oBAAI,IAAI,IAAG,IAAI,IAAI;AAAA,MACpC;AAAA,IACD;AAAA,EACD;AAEA,OAAK,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAC/B,YAAQ,MAAM,CAAC;AACf,UAAM,QAAQ,OAAO,CAAC;AACtB,WAAO,MAAM,IAAI,GAAG;AAEpB,QAAI,SAAS,QAAW;AACvB,UAAI,eAAe;AAAA;AAAA,QAAuC,QAAQ,EAAE;AAAA,UAAe;AAEnF,aAAO;AAAA,QACN;AAAA,QACAA;AAAA,QACA;AAAA,QACA,SAAS,OAAOA,OAAM,QAAQ,KAAK;AAAA,QACnC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAEA,YAAM,IAAI,KAAK,IAAI;AAEnB,gBAAU,CAAC;AACX,gBAAU,CAAC;AAEX,gBAAU,KAAK;AACf;AAAA,IACD;AAEA,QAAI,eAAe;AAClB,kBAAY,MAAM,OAAO,GAAG,KAAK;AAAA,IAClC;AAEA,SAAK,KAAK,EAAE,IAAI,WAAW,GAAG;AAC7B,oBAAc,KAAK,CAAC;AACpB,UAAI,aAAa;AAChB,mBAAK,MAAL,mBAAQ;AACR,SAAC,4BAAe,oBAAI,IAAI,IAAG,OAAO,IAAI;AAAA,MACvC;AAAA,IACD;AAEA,QAAI,SAAS,SAAS;AACrB,UAAI,SAAS,UAAa,KAAK,IAAI,IAAI,GAAG;AACzC,YAAI,QAAQ,SAAS,QAAQ,QAAQ;AAEpC,cAAI,QAAQ,QAAQ,CAAC;AACrB,cAAI;AAEJ,iBAAO,MAAM;AAEb,cAAI,IAAI,QAAQ,CAAC;AACjB,cAAI,IAAI,QAAQ,QAAQ,SAAS,CAAC;AAElC,eAAK,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK,GAAG;AACvC,iBAAK,QAAQ,CAAC,GAAG,OAAO,MAAM;AAAA,UAC/B;AAEA,eAAK,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK,GAAG;AACvC,iBAAK,OAAO,QAAQ,CAAC,CAAC;AAAA,UACvB;AAEA,eAAKA,QAAO,EAAE,MAAM,EAAE,IAAI;AAC1B,eAAKA,QAAO,MAAM,CAAC;AACnB,eAAKA,QAAO,GAAG,KAAK;AAEpB,oBAAU;AACV,iBAAO;AACP,eAAK;AAEL,oBAAU,CAAC;AACX,oBAAU,CAAC;AAAA,QACZ,OAAO;AAEN,eAAK,OAAO,IAAI;AAChB,eAAK,MAAM,SAAS,MAAM;AAE1B,eAAKA,QAAO,KAAK,MAAM,KAAK,IAAI;AAChC,eAAKA,QAAO,MAAM,SAAS,OAAOA,OAAM,QAAQ,KAAK,IAAI;AACzD,eAAKA,QAAO,MAAM,IAAI;AAEtB,iBAAO;AAAA,QACR;AAEA;AAAA,MACD;AAEA,gBAAU,CAAC;AACX,gBAAU,CAAC;AAEX,aAAO,YAAY,QAAQ,QAAQ,MAAM,KAAK;AAG7C,aAAK,QAAQ,EAAE,IAAI,WAAW,GAAG;AAChC,WAAC,gBAAS,oBAAI,IAAI,IAAG,IAAI,OAAO;AAAA,QACjC;AACA,gBAAQ,KAAK,OAAO;AACpB,kBAAU,QAAQ;AAAA,MACnB;AAEA,UAAI,YAAY,MAAM;AACrB;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAEA,YAAQ,KAAK,IAAI;AACjB,WAAO;AACP,cAAU,KAAK;AAAA,EAChB;AAEA,MAAI,YAAY,QAAQ,SAAS,QAAW;AAC3C,QAAI,aAAa,SAAS,SAAY,CAAC,IAAI,WAAW,IAAI;AAE1D,WAAO,YAAY,MAAM;AAExB,WAAK,QAAQ,EAAE,IAAI,WAAW,GAAG;AAChC,mBAAW,KAAK,OAAO;AAAA,MACxB;AACA,gBAAU,QAAQ;AAAA,IACnB;AAEA,QAAI,iBAAiB,WAAW;AAEhC,QAAI,iBAAiB,GAAG;AACvB,UAAI,qBAAqB,QAAQ,wBAAwB,KAAK,WAAW,IAAI,SAAS;AAEtF,UAAI,aAAa;AAChB,aAAK,IAAI,GAAG,IAAI,gBAAgB,KAAK,GAAG;AACvC,2BAAW,CAAC,EAAE,MAAd,mBAAiB;AAAA,QAClB;AAEA,aAAK,IAAI,GAAG,IAAI,gBAAgB,KAAK,GAAG;AACvC,2BAAW,CAAC,EAAE,MAAd,mBAAiB;AAAA,QAClB;AAAA,MACD;AAEA,oBAAcA,QAAO,YAAY,mBAAmB,KAAK;AAAA,IAC1D;AAAA,EACD;AAEA,MAAI,aAAa;AAChB,qBAAiB,MAAM;AA9czB,UAAAG;AA+cG,UAAI,eAAe,OAAW;AAC9B,WAAK,QAAQ,YAAY;AACxB,SAAAA,MAAA,KAAK,MAAL,gBAAAA,IAAQ;AAAA,MACT;AAAA,IACD,CAAC;AAAA,EACF;AAEsB,EAAC,cAAe,QAAQH,OAAM,SAASA,OAAM,MAAM;AACnD,EAAC,cAAe,OAAO,QAAQ,KAAK;AAC3D;AASA,SAAS,YAAY,MAAM,OAAOI,QAAO,MAAM;AAC9C,OAAK,OAAO,wBAAwB,GAAG;AACtC,iBAAa,KAAK,GAAG,KAAK;AAAA,EAC3B;AAEA,OAAK,OAAO,yBAAyB,GAAG;AACvC;AAAA;AAAA,MAA2C,KAAK;AAAA,MAAIA;AAAA,IAAK;AAAA,EAC1D,OAAO;AACN,SAAK,IAAIA;AAAA,EACV;AACD;AAgBA,SAAS,YACR,QACAJ,QACA,MACAK,OACA,OACA,KACAD,QACA,WACA,OACA,gBACC;AACD,MAAI,qBAAqB;AACzB,MAAI,YAAY,QAAQ,wBAAwB;AAChD,MAAI,WAAW,QAAQ,yBAAyB;AAEhD,MAAI,IAAI,WAAY,UAAU,eAAe,KAAK,IAAI,OAAO,KAAK,IAAK;AACvE,MAAI,KAAK,QAAQ,yBAAyB,IAAIA,SAAQ,OAAOA,MAAK;AAElE,MAAI,gBAAO,UAAU;AAGC,IAAC,EAAG,QAAQ,MAAM;AACtC,UAAI,mBAAmB,OAAO,MAAM,WAAWA,SAAQ,EAAE;AAEzD,qBAAe,EAAE,gBAAgB;AAAA,IAClC;AAAA,EACD;AAGA,MAAI,OAAO;AAAA,IACV;AAAA,IACA;AAAA,IACA,GAAG;AAAA,IACH,GAAG;AAAA;AAAA,IAEH,GAAG;AAAA,IACH;AAAA,IACA,MAAAC;AAAA,EACD;AAEA,sBAAoB;AAEpB,MAAI;AACH,SAAK,IAAI,OAAO,MAAM,UAAU,QAAQ,GAAG,GAAG,cAAc,GAAG,SAAS;AAExE,SAAK,EAAE,OAAO,QAAQ,KAAK;AAC3B,SAAK,EAAE,OAAOA,SAAQA,MAAK;AAE3B,QAAI,SAAS,MAAM;AAClB,MAAAL,OAAM,QAAQ;AAAA,IACf,OAAO;AACN,WAAK,OAAO;AACZ,WAAK,EAAE,OAAO,KAAK;AAAA,IACpB;AAEA,QAAIK,UAAS,MAAM;AAClB,MAAAA,MAAK,OAAO;AACZ,MAAAA,MAAK,EAAE,OAAO,KAAK;AAAA,IACpB;AAEA,WAAO;AAAA,EACR,UAAE;AACD,wBAAoB;AAAA,EACrB;AACD;AAOA,SAAS,KAAK,MAAMA,OAAM,QAAQ;AACjC,MAAI,MAAM,KAAK;AAAA;AAAA,IAAoC,KAAK,KAAK,EAAE;AAAA,MAAe;AAE9E,MAAI,OAAOA;AAAA;AAAA,IAAoCA,MAAK,EAAE;AAAA,MAAe;AACrE,MAAI;AAAA;AAAA,IAAoC,KAAK,EAAE;AAAA;AAE/C,SAAO,SAAS,KAAK;AACpB,QAAI;AAAA;AAAA,MAAyC,iBAAiB,IAAI;AAAA;AAClE,SAAK,OAAO,IAAI;AAChB,WAAO;AAAA,EACR;AACD;AAOA,SAAS,KAAKL,QAAO,MAAMK,OAAM;AAChC,MAAI,SAAS,MAAM;AAClB,IAAAL,OAAM,QAAQK;AAAA,EACf,OAAO;AACN,SAAK,OAAOA;AACZ,SAAK,EAAE,OAAOA,SAAQA,MAAK;AAAA,EAC5B;AAEA,MAAIA,UAAS,MAAM;AAClB,IAAAA,MAAK,OAAO;AACZ,IAAAA,MAAK,EAAE,OAAO,QAAQ,KAAK;AAAA,EAC5B;AACD;;;AC/kBA,SAAS,WAAWC,UAAS,aAAa,OAAO;AAlBjD;AAmBC,MAAI,CAAC,eAAe,gBAAgB,KAAK,OAAO,SAAS,EAAE,CAAC,EAAG;AAE/D,MAAI;AAGJ,QAAM,OAAM,KAAAA,SAAQ,kBAAR,mBAAuB;AACnC,MAAI,KAAK;AACR,eAAW,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM;AAAA,EACtD,YAAW,2DAAiC,WAAW;AACtD,eAAW,MAAM,+BAA+B,QAAQ,CAAC;AAAA,EAC1D;AAEA,EAAE,uBAAuB,kBAAkB,QAAQ,CAAC;AACrD;AAUO,SAAS,KAAK,MAAM,WAAW,MAAM,OAAO,SAAS,OAAO,eAAe,OAAO;AACxF,MAAI,SAAS;AAEb,MAAI,QAAQ;AAEZ,kBAAgB,MAAM;AACrB,QAAIC;AAAA;AAAA,MAAgC;AAAA;AAEpC,QAAI,WAAW,QAAQ,UAAU,KAAK,KAAK;AAC1C,UAAI,UAAW,cAAa;AAC5B;AAAA,IACD;AAEA,QAAIA,QAAO,gBAAgB,MAAM;AAChC;AAAA,QAAkBA,QAAO;AAAA;AAAA,QAA0CA,QAAO;AAAA,MAAU;AACpF,MAAAA,QAAO,cAAcA,QAAO,YAAY;AAAA,IACzC;AAEA,QAAI,UAAU,GAAI;AAElB,QAAI,WAAW;AAGd,UAAIC;AAAA;AAAA,QAA+B,aAAc;AAAA;AACjD,UAAIC,QAAO,aAAa;AACxB,UAAI,OAAOA;AAEX,aAAOA,UAAS,SAASA,MAAK,aAAa;AAAA,MAA6BA,MAAM,SAAS,KAAK;AAC3F,eAAOA;AACP,QAAAA;AAAA,QAAoC,iBAAiBA,KAAI;AAAA,MAC1D;AAEA,UAAIA,UAAS,MAAM;AAClB,QAAE,mBAAmB;AACrB,cAAM;AAAA,MACP;AAEA,UAAI,gBAAO,CAAC,cAAc;AACzB;AAAA;AAAA,UAAmCA,MAAK;AAAA,UAAaD;AAAA,UAAM;AAAA,QAAK;AAAA,MACjE;AAEA,mBAAa,cAAc,IAAI;AAC/B,eAAS,iBAAiBC,KAAI;AAC9B;AAAA,IACD;AAEA,QAAIC,QAAO,QAAQ;AACnB,QAAI,IAAK,CAAAA,QAAO,QAAQA,KAAI;AAAA,aACnB,OAAQ,CAAAA,QAAO,SAASA,KAAI;AAKrC,QAAIC,QAAO,0BAA0BD,KAAI;AAEzC,QAAI,OAAO,QAAQ;AAClB,MAAAC;AAAA,MAA+B,gBAAgBA,KAAI;AAAA,IACpD;AAEA;AAAA;AAAA,MAC8B,gBAAgBA,KAAI;AAAA;AAAA,MACpBA,MAAK;AAAA,IACnC;AAEA,QAAI,OAAO,QAAQ;AAClB,aAAO,gBAAgBA,KAAI,GAAG;AAC7B,eAAO;AAAA;AAAA,UAA4B,gBAAgBA,KAAI;AAAA,QAAE;AAAA,MAC1D;AAAA,IACD,OAAO;AACN,aAAO,OAAOA,KAAI;AAAA,IACnB;AAAA,EACD,CAAC;AACF;;;ACzGO,SAAS,KAAK,QAAQ,SAAS,MAAM,YAAY,aAAa;AATrE;AAUC,MAAI,WAAW;AACd,iBAAa;AAAA,EACd;AAEA,MAAI,WAAU,aAAQ,YAAR,mBAAkB;AAEhC,MAAI,aAAa;AACjB,MAAI,YAAY,MAAM;AACrB,cAAU,QAAQ,SAAS,YAAY,aAAa,IAAI;AACxD,iBAAa;AAAA,EACd;AAEA,MAAI,YAAY,QAAW;AAC1B,QAAI,gBAAgB,MAAM;AACzB,kBAAY,MAAM;AAAA,IACnB;AAAA,EACD,OAAO;AACN,YAAQ,QAAQ,aAAa,MAAM,aAAa,UAAU;AAAA,EAC3D;AACD;AAMO,SAAS,eAAe,OAAO;AAErC,QAAM,YAAY,CAAC;AACnB,MAAI,MAAM,SAAU,WAAU,UAAU;AACxC,aAAW,OAAO,MAAM,SAAS;AAChC,cAAU,GAAG,IAAI;AAAA,EAClB;AACA,SAAO;AACR;;;AC/BO,SAAS,8BAA8B,QAAQ;AACrD,QAAM,MAAM,OAAO;AACnB,MAAI,OAAO,QAAQ,GAAG,GAAG;AACxB,IAAE,6BAA6B,GAAG;AAAA,EACnC;AACD;AAGO,SAAS,6BAA6B,QAAQ;AACpD,QAAM,MAAM,OAAO;AACnB,QAAM,YAAY,OAAO,QAAQ;AACjC,MAAI,OAAO,CAAC,WAAW;AACtB,IAAE,kCAAkC;AAAA,EACrC;AACD;AAMO,SAAS,eAAe,OAAO,MAAM;AAC3C,MAAI,SAAS,QAAQ,OAAO,MAAM,cAAc,YAAY;AAC3D,IAAE,oBAAoB,IAAI;AAAA,EAC3B;AACD;AAMO,SAAS,gCAAgC,IAAI;AACnD,KAAG,WAAW,MAAM;AACnB,IAAE,2BAA2B;AAC7B,WAAO;AAAA,EACR;AACA,SAAO;AACR;;;ACtBO,SAAS,QAAQ,MAAM,gBAAgB,MAAM;AACnD,MAAI,SAAS;AAIb,MAAIC,WAAU;AAGd,MAAI;AAEJ,QAAM,MAAM;AACX,QAAIA,cAAaA,WAAU,YAAY,GAAI;AAE3C,QAAI,gBAAgB;AACnB,qBAAe,cAAc;AAC7B,uBAAiB;AAAA,IAClB;AAEA,QAAI,gBAAOA,YAAW,MAAM;AAC3B,MAAE,gBAAgB;AAAA,IACnB;AAEA,qBAAiB,OAAO;AAAA;AAAA,MAAgCA,SAAS,QAAQ,GAAG,IAAI;AAAA,KAAC;AAAA,EAClF,GAAG,kBAAkB;AAErB,MAAI,WAAW;AACd,aAAS;AAAA,EACV;AACD;AAQO,SAAS,aAAaC,YAAW,IAAI;AAC3C,QAAMD,WAAU,CAA6B,SAA8B,SAAS;AACnF,QAAI,8BAA8B;AAClC,uCAAmCC,UAAS;AAE5C,QAAI;AACH,aAAO,GAAG,MAAM,GAAG,IAAI;AAAA,IACxB,UAAE;AACD,yCAAmC,2BAA2B;AAAA,IAC/D;AAAA,EACD;AAEA,kCAAgCD,QAAO;AAEvC,SAAOA;AACR;AAWO,SAAS,iBAAiB,IAAI;AAEpC,SAAO,CAA6B,WAA0C,WAAW;AA1F1F;AA2FE,QAAIA,WAAU,GAAG,GAAG,MAAM;AAG1B,QAAIE;AAEJ,QAAI,WAAW;AACd,MAAAA;AAAA,MAAkC;AAClC,mBAAa;AAAA,IACd,OAAO;AACN,UAAIC,QAAOH,SAAQ,OAAO,EAAE,KAAK;AACjC,UAAI,WAAW,0BAA0BG,KAAI;AAC7C,MAAAD;AAAA,MAAkC,gBAAgB,QAAQ;AAE1D,UAAI,iBAAQ,iBAAiBA,QAAO,MAAM,QAAQA,SAAQ,aAAa,IAAI;AAC1E,QAAE,2BAA2B;AAAA,MAC9B;AAEA,aAAO,OAAOA,QAAO;AAAA,IACtB;AAEA,UAAM,UAAS,KAAAF,SAAQ,UAAR,wBAAAA,UAAgBE;AAC/B,iBAAaA,UAASA,QAAO;AAE7B,QAAI,OAAO,WAAW,YAAY;AACjC,eAAS,MAAM;AAAA,IAChB;AAAA,EACD;AACD;;;ACzGO,SAAS,UAAU,MAAM,eAAe,WAAW;AACzD,MAAI,WAAW;AACd,iBAAa;AAAA,EACd;AAEA,MAAI,SAAS;AAGb,MAAIE;AAGJ,MAAIC;AAEJ,QAAM,MAAM;AACX,QAAID,gBAAeA,aAAY,cAAc,GAAI;AAEjD,QAAIC,SAAQ;AACX,mBAAaA,OAAM;AACnB,MAAAA,UAAS;AAAA,IACV;AAEA,QAAID,YAAW;AACd,MAAAC,UAAS,OAAO,MAAM,UAAU,QAAQD,UAAS,CAAC;AAAA,IACnD;AAAA,EACD,GAAG,kBAAkB;AAErB,MAAI,WAAW;AACd,aAAS;AAAA,EACV;AACD;;;ACPO,SAAS,QAAQ,MAAM,SAAS,QAAQ,WAAW,eAAe,UAAU;AAnCnF;AAoCC,MAAI,gBAAgB;AAEpB,MAAI,WAAW;AACd,iBAAa;AAAA,EACd;AAEA,MAAI,WAAW,gBAAO,cAAY,8CAAmB,SAAS;AAG9D,MAAI;AAGJ,MAAI;AAGJ,MAAIE,WAAU;AAEd,MAAI,aAAa,aAAa,aAAa,GAAG;AAC7C,IAAAA;AAAA,IAAkC;AAClC,iBAAa;AAAA,EACd;AAEA,MAAI;AAAA;AAAA,IAAsC,YAAY,eAAe;AAAA;AAGrE,MAAIC;AAOJ,MAAI,kBAAkB;AAEtB,QAAM,MAAM;AACX,UAAM,WAAW,QAAQ,KAAK;AAC9B,QAAI,KAAK,gBAAgB,cAAc,IAAI,UAAU,aAAa,QAAQ,gBAAgB;AAG1F,QAAI,aAAa,IAAK;AAGtB,QAAI,qBAAqB;AACzB,0BAAsB,eAAe;AAErC,QAAIA,SAAQ;AACX,UAAI,aAAa,MAAM;AAEtB,qBAAaA,SAAQ,MAAM;AAC1B,UAAAA,UAAS;AACT,wBAAc;AAAA,QACf,CAAC;AAAA,MACF,WAAW,aAAa,aAAa;AAEpC,sBAAcA,OAAM;AAAA,MACrB,OAAO;AAEN,uBAAeA,OAAM;AACrB,yBAAiB,KAAK;AAAA,MACvB;AAAA,IACD;AAEA,QAAI,YAAY,aAAa,aAAa;AACzC,MAAAA,UAAS,OAAO,MAAM;AACrB,QAAAD,WAAU;AAAA;AAAA,UACiBA;AAAA,YACxB,KACC,SAAS,gBAAgB,IAAI,QAAQ,IACrC,SAAS,cAAc,QAAQ;AAEnC,YAAI,gBAAO,UAAU;AAEpB,UAAAA,SAAQ,gBAAgB;AAAA,YACvB,KAAK;AAAA,cACJ,MAAM;AAAA,cACN,MAAM,SAAS,CAAC;AAAA,cAChB,QAAQ,SAAS,CAAC;AAAA,YACnB;AAAA,UACD;AAAA,QACD;AAEA,qBAAaA,UAASA,QAAO;AAE7B,YAAI,WAAW;AACd,cAAI,aAAa,oBAAoB,QAAQ,GAAG;AAE/C,YAAAA,SAAQ,OAAO,SAAS,cAAc,EAAE,CAAC;AAAA,UAC1C;AAIA,cAAI;AAAA;AAAA,YACH,YAAY,gBAAgBA,QAAO,IAAIA,SAAQ,YAAY,YAAY,CAAC;AAAA;AAGzE,cAAI,WAAW;AACd,gBAAI,iBAAiB,MAAM;AAC1B,4BAAc,KAAK;AAAA,YACpB,OAAO;AACN,+BAAiB,YAAY;AAAA,YAC9B;AAAA,UACD;AAMA,oBAAUA,UAAS,YAAY;AAAA,QAChC;AAGsB,QAAC,cAAe,YAAYA;AAElD,eAAO,OAAOA,QAAO;AAAA,MACtB,CAAC;AAAA,IACF;AAEA,UAAM;AACN,QAAI,IAAK,eAAc;AACvB,qBAAiB,IAAI;AAErB,0BAAsB,kBAAkB;AAAA,EACzC,GAAG,kBAAkB;AAErB,MAAI,eAAe;AAClB,kBAAc,IAAI;AAClB,qBAAiB,MAAM;AAAA,EACxB;AACD;;;AC5JO,SAAS,cAAc,QAAQ,KAAK;AAE1C,mBAAiB,MAAM;AACtB,QAAI,OAAO,OAAO,YAAY;AAE9B,QAAI;AAAA;AAAA,MAAoC,KAAM;AAAA;AAAA,QAChB;AAAA;AAAA;AAAA,QACF,KAAM;AAAA,QAAiC,KAAK,cAAe;AAAA;AAAA;AAIvF,QAAI,CAAC,OAAO,cAAc,MAAM,IAAI,IAAI,GAAG;AAC1C,YAAM,QAAQ,SAAS,cAAc,OAAO;AAC5C,YAAM,KAAK,IAAI;AACf,YAAM,cAAc,IAAI;AAExB,aAAO,YAAY,KAAK;AAExB,UAAI,cAAK;AACR,uBAAe,IAAI,MAAM,KAAK;AAAA,MAC/B;AAAA,IACD;AAAA,EACD,CAAC;AACF;;;ACnBO,SAAS,OAAO,KAAKE,SAAQ,WAAW;AAC9C,SAAO,MAAM;AACZ,QAAI,UAAU,QAAQ,MAAMA,QAAO,KAAK,wCAAa,KAAK,CAAC,CAAC;AAE5D,QAAI,cAAa,mCAAS,SAAQ;AACjC,UAAI,SAAS;AAEb,UAAI;AAAA;AAAA,QAA2B,CAAC;AAAA;AAEhC,oBAAc,MAAM;AACnB,YAAI,QAAQ,UAAU;AAKtB,wBAAgB,KAAK;AAErB,YAAI,UAAU,eAAe,MAAM,KAAK,GAAG;AAC1C,iBAAO;AACiB,UAAC,QAAQ,OAAQ,KAAK;AAAA,QAC/C;AAAA,MACD,CAAC;AAED,eAAS;AAAA,IACV;AAEA,QAAI,mCAAS,SAAS;AACrB,aAAO;AAAA;AAAA,QAA+B,QAAQ,QAAS;AAAA;AAAA,IACxD;AAAA,EACD,CAAC;AACF;;;AC/BO,SAAS,OAAO,MAAM,QAAQ;AAEpC,MAAI,KAAK;AAGT,MAAI;AAEJ,QAAM,MAAM;AACX,QAAI,QAAQ,KAAK,OAAO,IAAI;AAC3B,UAAI,GAAG;AACN,uBAAe,CAAC;AAChB,YAAI;AAAA,MACL;AAEA,UAAI,IAAI;AACP,YAAI,OAAO,MAAM;AAChB,iBAAO;AAAA;AAAA,YAA8C,GAAI,IAAI;AAAA,WAAC;AAAA,QAC/D,CAAC;AAAA,MACF;AAAA,IACD;AAAA,EACD,CAAC;AACF;;;AChCA,IAAM,aAAa;AACnB,IAAM,gBAAgB;AAOf,SAAS,YAAY,OAAO,SAAS;AAC3C,QAAM,MAAM,OAAO,SAAS,EAAE;AAE9B,QAAM,UAAU,UAAU,aAAa;AACvC,UAAQ,YAAY;AAEpB,MAAI,UAAU;AACd,MAAI,OAAO;AAEX,SAAO,QAAQ,KAAK,GAAG,GAAG;AACzB,UAAM,IAAI,QAAQ,YAAY;AAC9B,UAAM,KAAK,IAAI,CAAC;AAChB,eAAW,IAAI,UAAU,MAAM,CAAC,KAAK,OAAO,MAAM,UAAU,OAAO,MAAM,WAAW;AACpF,WAAO,IAAI;AAAA,EACZ;AAEA,SAAO,UAAU,IAAI,UAAU,IAAI;AACpC;;;AChBA,IAAM,eAAe;AAAA,EACpB,WAAW,oBAAI,IAAI;AAAA,IAClB,CAAC,MAAM,KAAK;AAAA,IACZ,CAAC,OAAO,IAAI;AAAA,EACb,CAAC;AACF;AASO,SAAS,KAAK,MAAM,OAAO,aAAa,OAAO;AACrD,MAAI,SAAS,QAAS,CAAC,SAAS,WAAa,QAAO;AACpD,QAAM,aAAc,QAAQ,gBAAgB,aAAa,IAAI,EAAE,IAAI,KAAK,KAAM;AAC9E,QAAM,aAAa,aAAa,KAAK,KAAK,YAAY,YAAY,IAAI,CAAC;AACvE,SAAO,IAAI,IAAI,GAAG,UAAU;AAC7B;AAOO,SAASC,MAAK,OAAO;AAC3B,MAAI,OAAO,UAAU,UAAU;AAC9B,WAAO,KAAM,KAAK;AAAA,EACnB,OAAO;AACN,WAAO,SAAS;AAAA,EACjB;AACD;AAEA,IAAM,aAAa,CAAC,GAAG,mBAA6B;AAQ7C,SAAS,SAAS,OAAOC,OAAM,YAAY;AACjD,MAAI,YAAY,SAAS,OAAO,KAAK,KAAK;AAE1C,MAAIA,OAAM;AACT,gBAAY,YAAY,YAAY,MAAMA,QAAOA;AAAA,EAClD;AAEA,MAAI,YAAY;AACf,aAAS,OAAO,YAAY;AAC3B,UAAI,WAAW,GAAG,GAAG;AACpB,oBAAY,YAAY,YAAY,MAAM,MAAM;AAAA,MACjD,WAAW,UAAU,QAAQ;AAC5B,YAAI,MAAM,IAAI;AACd,YAAI,IAAI;AAER,gBAAQ,IAAI,UAAU,QAAQ,KAAK,CAAC,MAAM,GAAG;AAC5C,cAAI,IAAI,IAAI;AAEZ,eACE,MAAM,KAAK,WAAW,SAAS,UAAU,IAAI,CAAC,CAAC,OAC/C,MAAM,UAAU,UAAU,WAAW,SAAS,UAAU,CAAC,CAAC,IAC1D;AACD,yBAAa,MAAM,IAAI,KAAK,UAAU,UAAU,GAAG,CAAC,KAAK,UAAU,UAAU,IAAI,CAAC;AAAA,UACnF,OAAO;AACN,gBAAI;AAAA,UACL;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAEA,SAAO,cAAc,KAAK,OAAO;AAClC;AAOA,SAASC,eAAc,QAAQ,YAAY,OAAO;AACjD,MAAI,YAAY,YAAY,iBAAiB;AAC7C,MAAI,MAAM;AAEV,WAAS,OAAO,QAAQ;AACvB,QAAI,QAAQ,OAAO,GAAG;AACtB,QAAI,SAAS,QAAQ,UAAU,IAAI;AAClC,aAAO,MAAM,MAAM,OAAO,QAAQ;AAAA,IACnC;AAAA,EACD;AAEA,SAAO;AACR;AAMA,SAAS,YAAY,MAAM;AAC1B,MAAI,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,MAAM,KAAK;AACvC,WAAO,KAAK,YAAY;AAAA,EACzB;AACA,SAAO;AACR;AAOO,SAAS,SAAS,OAAO,QAAQ;AACvC,MAAI,QAAQ;AACX,QAAI,YAAY;AAGhB,QAAI;AAGJ,QAAI;AAEJ,QAAI,MAAM,QAAQ,MAAM,GAAG;AAC1B,sBAAgB,OAAO,CAAC;AACxB,yBAAmB,OAAO,CAAC;AAAA,IAC5B,OAAO;AACN,sBAAgB;AAAA,IACjB;AAEA,QAAI,OAAO;AACV,cAAQ,OAAO,KAAK,EAClB,WAAW,sBAAsB,EAAE,EACnC,KAAK;AAGP,UAAI,SAAS;AACb,UAAI,SAAS;AACb,UAAI,aAAa;AAEjB,UAAI,iBAAiB,CAAC;AAEtB,UAAI,eAAe;AAClB,uBAAe,KAAK,GAAG,OAAO,KAAK,aAAa,EAAE,IAAI,WAAW,CAAC;AAAA,MACnE;AACA,UAAI,kBAAkB;AACrB,uBAAe,KAAK,GAAG,OAAO,KAAK,gBAAgB,EAAE,IAAI,WAAW,CAAC;AAAA,MACtE;AAEA,UAAI,cAAc;AAClB,UAAI,aAAa;AAEjB,YAAM,MAAM,MAAM;AAClB,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC7B,YAAI,IAAI,MAAM,CAAC;AAEf,YAAI,YAAY;AACf,cAAI,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,KAAK;AACtC,yBAAa;AAAA,UACd;AAAA,QACD,WAAW,QAAQ;AAClB,cAAI,WAAW,GAAG;AACjB,qBAAS;AAAA,UACV;AAAA,QACD,WAAW,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,KAAK;AAC7C,uBAAa;AAAA,QACd,WAAW,MAAM,OAAO,MAAM,KAAK;AAClC,mBAAS;AAAA,QACV,WAAW,MAAM,KAAK;AACrB;AAAA,QACD,WAAW,MAAM,KAAK;AACrB;AAAA,QACD;AAEA,YAAI,CAAC,cAAc,WAAW,SAAS,WAAW,GAAG;AACpD,cAAI,MAAM,OAAO,eAAe,IAAI;AACnC,yBAAa;AAAA,UACd,WAAW,MAAM,OAAO,MAAM,MAAM,GAAG;AACtC,gBAAI,eAAe,IAAI;AACtB,kBAAI,OAAO,YAAY,MAAM,UAAU,aAAa,UAAU,EAAE,KAAK,CAAC;AAEtE,kBAAI,CAAC,eAAe,SAAS,IAAI,GAAG;AACnC,oBAAI,MAAM,KAAK;AACd;AAAA,gBACD;AAEA,oBAAI,WAAW,MAAM,UAAU,aAAa,CAAC,EAAE,KAAK;AACpD,6BAAa,MAAM,WAAW;AAAA,cAC/B;AAAA,YACD;AAEA,0BAAc,IAAI;AAClB,yBAAa;AAAA,UACd;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,QAAI,eAAe;AAClB,mBAAaA,eAAc,aAAa;AAAA,IACzC;AAEA,QAAI,kBAAkB;AACrB,mBAAaA,eAAc,kBAAkB,IAAI;AAAA,IAClD;AAEA,gBAAY,UAAU,KAAK;AAC3B,WAAO,cAAc,KAAK,OAAO;AAAA,EAClC;AAEA,SAAO,SAAS,OAAO,OAAO,OAAO,KAAK;AAC3C;;;AC9MO,SAAS,UAAU,KAAK,SAAS,OAAOC,OAAM,cAAc,cAAc;AAEhF,MAAI,OAAO,IAAI;AAEf,MACC,aACA,SAAS,SACT,SAAS,QACR;AACD,QAAI,kBAAkB,SAAS,OAAOA,OAAM,YAAY;AAExD,QAAI,CAAC,aAAa,oBAAoB,IAAI,aAAa,OAAO,GAAG;AAKhE,UAAI,mBAAmB,MAAM;AAC5B,YAAI,gBAAgB,OAAO;AAAA,MAC5B,WAAW,SAAS;AACnB,YAAI,YAAY;AAAA,MACjB,OAAO;AACN,YAAI,aAAa,SAAS,eAAe;AAAA,MAC1C;AAAA,IACD;AAGA,QAAI,cAAc;AAAA,EACnB,WAAW,gBAAgB,iBAAiB,cAAc;AACzD,aAAS,OAAO,cAAc;AAC7B,UAAI,aAAa,CAAC,CAAC,aAAa,GAAG;AAEnC,UAAI,gBAAgB,QAAQ,eAAe,CAAC,CAAC,aAAa,GAAG,GAAG;AAC/D,YAAI,UAAU,OAAO,KAAK,UAAU;AAAA,MACrC;AAAA,IACD;AAAA,EACD;AAEA,SAAO;AACR;;;ACzCA,SAAS,cAAc,KAAK,OAAO,CAAC,GAAGC,OAAM,UAAU;AACtD,WAAS,OAAOA,OAAM;AACrB,QAAI,QAAQA,MAAK,GAAG;AAEpB,QAAI,KAAK,GAAG,MAAM,OAAO;AACxB,UAAIA,MAAK,GAAG,KAAK,MAAM;AACtB,YAAI,MAAM,eAAe,GAAG;AAAA,MAC7B,OAAO;AACN,YAAI,MAAM,YAAY,KAAK,OAAO,QAAQ;AAAA,MAC3C;AAAA,IACD;AAAA,EACD;AACD;AAQO,SAAS,UAAU,KAAK,OAAO,aAAa,aAAa;AAE/D,MAAI,OAAO,IAAI;AAEf,MAAI,aAAa,SAAS,OAAO;AAChC,QAAI,kBAAkB,SAAS,OAAO,WAAW;AAEjD,QAAI,CAAC,aAAa,oBAAoB,IAAI,aAAa,OAAO,GAAG;AAChE,UAAI,mBAAmB,MAAM;AAC5B,YAAI,gBAAgB,OAAO;AAAA,MAC5B,OAAO;AACN,YAAI,MAAM,UAAU;AAAA,MACrB;AAAA,IACD;AAGA,QAAI,UAAU;AAAA,EACf,WAAW,aAAa;AACvB,QAAI,MAAM,QAAQ,WAAW,GAAG;AAC/B,oBAAc,KAAK,2CAAc,IAAI,YAAY,CAAC,CAAC;AACnD,oBAAc,KAAK,2CAAc,IAAI,YAAY,CAAC,GAAG,WAAW;AAAA,IACjE,OAAO;AACN,oBAAc,KAAK,aAAa,WAAW;AAAA,IAC5C;AAAA,EACD;AAEA,SAAO;AACR;;;ACnCO,IAAM,QAAQ,OAAO,OAAO;AAC5B,IAAM,QAAQ,OAAO,OAAO;AAEnC,IAAM,oBAAoB,OAAO,mBAAmB;AACpD,IAAM,UAAU,OAAO,SAAS;AAQzB,SAAS,sBAAsB,OAAO;AAC5C,MAAI,CAAC,UAAW;AAEhB,MAAI,kBAAkB;AAMtB,MAAI,kBAAkB,MAAM;AAC3B,QAAI,gBAAiB;AACrB,sBAAkB;AAGlB,QAAI,MAAM,aAAa,OAAO,GAAG;AAChC,UAAI,QAAQ,MAAM;AAClB,oBAAc,OAAO,SAAS,IAAI;AAClC,YAAM,QAAQ;AAAA,IACf;AAEA,QAAI,MAAM,aAAa,SAAS,GAAG;AAClC,UAAI,UAAU,MAAM;AACpB,oBAAc,OAAO,WAAW,IAAI;AACpC,YAAM,UAAU;AAAA,IACjB;AAAA,EACD;AAGA,QAAM,SAAS;AACf,kBAAgB,eAAe;AAC/B,0BAAwB;AACzB;AAMO,SAAS,UAAUC,UAAS,OAAO;AACzC,MAAI,aAAa,eAAeA,QAAO;AAEvC,MACC,WAAW,WACT,WAAW;AAAA,EAEX,SAAS;AAAA;AAAA,EAGVA,SAAQ,UAAU,UAAU,UAAU,KAAKA,SAAQ,aAAa,aAChE;AACD;AAAA,EACD;AAGA,EAAAA,SAAQ,QAAQ,SAAS;AAC1B;AAMO,SAAS,YAAYA,UAAS,SAAS;AAC7C,MAAI,aAAa,eAAeA,QAAO;AAEvC,MACC,WAAW,aACV,WAAW;AAAA,EAEX,WAAW,SACX;AACD;AAAA,EACD;AAGA,EAAAA,SAAQ,UAAU;AACnB;AASO,SAAS,aAAaA,UAAS,UAAU;AAC/C,MAAI,UAAU;AAGb,QAAI,CAACA,SAAQ,aAAa,UAAU,GAAG;AACtC,MAAAA,SAAQ,aAAa,YAAY,EAAE;AAAA,IACpC;AAAA,EACD,OAAO;AACN,IAAAA,SAAQ,gBAAgB,UAAU;AAAA,EACnC;AACD;AAOO,SAAS,oBAAoBA,UAAS,SAAS;AACrD,QAAM,iBAAiBA,SAAQ;AAC/B,EAAAA,SAAQ,iBAAiB;AACzB,EAAAA,SAAQ,UAAU;AACnB;AAOO,SAAS,kBAAkBA,UAAS,OAAO;AACjD,QAAM,iBAAiBA,SAAQ;AAC/B,EAAAA,SAAQ,eAAe;AACvB,EAAAA,SAAQ,QAAQ;AACjB;AAQO,SAAS,cAAcA,UAAS,WAAW,OAAO,cAAc;AACtE,MAAI,aAAa,eAAeA,QAAO;AAEvC,MAAI,WAAW;AACd,eAAW,SAAS,IAAIA,SAAQ,aAAa,SAAS;AAEtD,QACC,cAAc,SACd,cAAc,YACb,cAAc,UAAUA,SAAQ,aAAa,QAC7C;AACD,UAAI,CAAC,cAAc;AAClB,mCAA2BA,UAAS,WAAW,SAAS,EAAE;AAAA,MAC3D;AAMA;AAAA,IACD;AAAA,EACD;AAEA,MAAI,WAAW,SAAS,OAAO,WAAW,SAAS,IAAI,OAAQ;AAE/D,MAAI,cAAc,WAAW;AAE5B,IAAAA,SAAQ,mBAAmB,IAAI;AAAA,EAChC;AAEA,MAAI,SAAS,MAAM;AAClB,IAAAA,SAAQ,gBAAgB,SAAS;AAAA,EAClC,WAAW,OAAO,UAAU,YAAY,YAAYA,QAAO,EAAE,SAAS,SAAS,GAAG;AAEjF,IAAAA,SAAQ,SAAS,IAAI;AAAA,EACtB,OAAO;AACN,IAAAA,SAAQ,aAAa,WAAW,KAAK;AAAA,EACtC;AACD;AAOO,SAAS,oBAAoB,KAAK,WAAW,OAAO;AAC1D,MAAI,eAAe,gCAAgC,WAAW,KAAK;AACpE;AAOO,SAAS,wBAAwB,MAAMC,OAAM,OAAO;AAK1D,MAAI,oBAAoB;AACxB,MAAI,kBAAkB;AAItB,MAAI,gBAAgB;AACpB,MAAI,WAAW;AACd,kBAAc,KAAK;AAAA,EACpB;AAEA,sBAAoB,IAAI;AACxB,oBAAkB,IAAI;AAEtB,MAAI;AACH;AAAA;AAAA,MAECA,UAAS;AAAA;AAAA;AAAA,OAIR,cAAc,IAAI,KAAK,QAAQ;AAAA,MAEhC,CAAC,kBACD,eAAe,IAAI,KAAK,QAAQ,YAAY,CAAC,IAC1C,YAAY,IAAI,EAAE,SAASA,KAAI,IAC/B,SAAS,OAAO,UAAU;AAAA,MAC5B;AAED,WAAKA,KAAI,IAAI;AAAA,IACd,OAAO;AAIN,oBAAc,MAAMA,OAAM,SAAS,OAAO,QAAQ,OAAO,KAAK,CAAC;AAAA,IAChE;AAAA,EACD,UAAE;AACD,wBAAoB,iBAAiB;AACrC,sBAAkB,eAAe;AACjC,QAAI,eAAe;AAClB,oBAAc,IAAI;AAAA,IACnB;AAAA,EACD;AACD;AAWO,SAAS,eAAeD,UAAS,MAAME,OAAM,UAAU,eAAe,OAAO;AACnF,MAAI,aAAa,eAAeF,QAAO;AAEvC,MAAI,oBAAoB,WAAW,iBAAiB;AACpD,MAAI,0BAA0B,CAAC,WAAW,OAAO;AAIjD,MAAI,8BAA8B,aAAa;AAC/C,MAAI,6BAA6B;AAChC,kBAAc,KAAK;AAAA,EACpB;AAEA,MAAI,UAAU,QAAQ,CAAC;AACvB,MAAI,oBAAoBA,SAAQ,YAAY;AAE5C,WAAS,OAAO,MAAM;AACrB,QAAI,EAAE,OAAOE,QAAO;AACnB,MAAAA,MAAK,GAAG,IAAI;AAAA,IACb;AAAA,EACD;AAEA,MAAIA,MAAK,OAAO;AACf,IAAAA,MAAK,QAAQC,MAAKD,MAAK,KAAK;AAAA,EAC7B,WAAW,YAAYA,MAAK,KAAK,GAAG;AACnC,IAAAA,MAAK,QAAQ;AAAA,EACd;AAEA,MAAIA,MAAK,KAAK,GAAG;AAChB,IAAAA,MAAK,UAALA,MAAK,QAAU;AAAA,EAChB;AAEA,MAAI,UAAU,YAAYF,QAAO;AAGjC,aAAWI,QAAOF,OAAM;AAEvB,QAAI,QAAQA,MAAKE,IAAG;AAIpB,QAAI,qBAAqBA,SAAQ,WAAW,SAAS,MAAM;AAY1D,MAAAJ,SAAQ,QAAQA,SAAQ,UAAU;AAClC,cAAQI,IAAG,IAAI;AACf;AAAA,IACD;AAEA,QAAIA,SAAQ,SAAS;AACpB,UAAI,UAAUJ,SAAQ,iBAAiB;AACvC,gBAAUA,UAAS,SAAS,OAAO,UAAU,6BAAO,QAAQE,MAAK,KAAK,CAAC;AACvE,cAAQE,IAAG,IAAI;AACf,cAAQ,KAAK,IAAIF,MAAK,KAAK;AAC3B;AAAA,IACD;AAEA,QAAIE,SAAQ,SAAS;AACpB,gBAAUJ,UAAS,OAAO,6BAAO,QAAQE,MAAK,KAAK,CAAC;AACpD,cAAQE,IAAG,IAAI;AACf,cAAQ,KAAK,IAAIF,MAAK,KAAK;AAC3B;AAAA,IACD;AAEA,QAAI,aAAa,QAAQE,IAAG;AAC5B,QAAI,UAAU,WAAY;AAE1B,YAAQA,IAAG,IAAI;AAEf,QAAI,SAASA,KAAI,CAAC,IAAIA,KAAI,CAAC;AAC3B,QAAI,WAAW,KAAM;AAErB,QAAI,WAAW,MAAM;AAEpB,YAAM,OAAO,CAAC;AACd,YAAM,mBAAmB,OAAOA;AAChC,UAAI,aAAaA,KAAI,MAAM,CAAC;AAC5B,UAAI,YAAY,aAAa,UAAU;AAEvC,UAAI,iBAAiB,UAAU,GAAG;AACjC,qBAAa,WAAW,MAAM,GAAG,EAAE;AACnC,aAAK,UAAU;AAAA,MAChB;AAEA,UAAI,CAAC,aAAa,YAAY;AAK7B,YAAI,SAAS,KAAM;AAEnB,QAAAJ,SAAQ,oBAAoB,YAAY,QAAQ,gBAAgB,GAAG,IAAI;AACvE,gBAAQ,gBAAgB,IAAI;AAAA,MAC7B;AAEA,UAAI,SAAS,MAAM;AAClB,YAAI,CAAC,WAAW;AAKf,cAAS,SAAT,SAAgB,KAAK;AACpB,oBAAQI,IAAG,EAAE,KAAK,MAAM,GAAG;AAAA,UAC5B;AAEA,kBAAQ,gBAAgB,IAAI,aAAa,YAAYJ,UAAS,QAAQ,IAAI;AAAA,QAC3E,OAAO;AAEN,UAAAA,SAAQ,KAAK,UAAU,EAAE,IAAI;AAC7B,mBAAS,CAAC,UAAU,CAAC;AAAA,QACtB;AAAA,MACD,WAAW,WAAW;AAErB,QAAAA,SAAQ,KAAK,UAAU,EAAE,IAAI;AAAA,MAC9B;AAAA,IACD,WAAWI,SAAQ,SAAS;AAE3B,oBAAcJ,UAASI,MAAK,KAAK;AAAA,IAClC,WAAWA,SAAQ,aAAa;AAC/B;AAAA;AAAA,QAAsCJ;AAAA,QAAU,QAAQ,KAAK;AAAA,MAAC;AAAA,IAC/D,WAAW,CAAC,sBAAsBI,SAAQ,aAAcA,SAAQ,WAAW,SAAS,OAAQ;AAG3F,MAAAJ,SAAQ,QAAQA,SAAQ,UAAU;AAAA,IACnC,WAAWI,SAAQ,cAAc,mBAAmB;AACnD;AAAA;AAAA,QAA+CJ;AAAA,QAAU;AAAA,MAAK;AAAA,IAC/D,OAAO;AACN,UAAI,OAAOI;AACX,UAAI,CAAC,yBAAyB;AAC7B,eAAO,oBAAoB,IAAI;AAAA,MAChC;AAEA,UAAI,aAAa,SAAS,kBAAkB,SAAS;AAErD,UAAI,SAAS,QAAQ,CAAC,qBAAqB,CAAC,YAAY;AACvD,mBAAWA,IAAG,IAAI;AAElB,YAAI,SAAS,WAAW,SAAS,WAAW;AAE3C,cAAI;AAAA;AAAA,YAAyCJ;AAAA;AAC7C,gBAAM,cAAc,SAAS;AAC7B,cAAI,SAAS,SAAS;AACrB,gBAAI,WAAW,MAAM;AACrB,kBAAM,gBAAgB,IAAI;AAC1B,kBAAM,eAAe;AAErB,kBAAM,QAAQ,MAAM,UAAU,cAAc,WAAW;AAAA,UACxD,OAAO;AACN,gBAAI,WAAW,MAAM;AACrB,kBAAM,gBAAgB,IAAI;AAC1B,kBAAM,iBAAiB;AACvB,kBAAM,UAAU,cAAc,WAAW;AAAA,UAC1C;AAAA,QACD,OAAO;AACN,UAAAA,SAAQ,gBAAgBI,IAAG;AAAA,QAC5B;AAAA,MACD,WACC,cACC,QAAQ,SAAS,IAAI,MAAM,qBAAqB,OAAO,UAAU,WACjE;AAED,QAAAJ,SAAQ,IAAI,IAAI;AAAA,MACjB,WAAW,OAAO,UAAU,YAAY;AACvC,sBAAcA,UAAS,MAAM,OAAO,YAAY;AAAA,MACjD;AAAA,IACD;AAAA,EACD;AAEA,MAAI,6BAA6B;AAChC,kBAAc,IAAI;AAAA,EACnB;AAEA,WAAS,UAAU,OAAO,sBAAsBE,KAAI,GAAG;AACtD,QAAI,OAAO,gBAAgB,gBAAgB;AAC1C,aAAOF,UAAS,MAAME,MAAK,MAAM,CAAC;AAAA,IACnC;AAAA,EACD;AAEA,SAAO;AACR;AAMA,SAAS,eAAeF,UAAS;AAChC;AAAA;AAAA;AAAA,IAECA,SAAQ,iBAARA,SAAQ,eAAiB;AAAA,MACxB,CAAC,iBAAiB,GAAGA,SAAQ,SAAS,SAAS,GAAG;AAAA,MAClD,CAAC,OAAO,GAAGA,SAAQ,iBAAiB;AAAA,IACrC;AAAA;AAEF;AAGA,IAAI,gBAAgB,oBAAI,IAAI;AAG5B,SAAS,YAAYA,UAAS;AAC7B,MAAI,UAAU,cAAc,IAAIA,SAAQ,QAAQ;AAChD,MAAI,QAAS,QAAO;AACpB,gBAAc,IAAIA,SAAQ,UAAW,UAAU,CAAC,CAAE;AAElD,MAAI;AACJ,MAAI,QAAQA;AACZ,MAAI,gBAAgB,QAAQ;AAI5B,SAAO,kBAAkB,OAAO;AAC/B,kBAAc,gBAAgB,KAAK;AAEnC,aAAS,OAAO,aAAa;AAC5B,UAAI,YAAY,GAAG,EAAE,KAAK;AACzB,gBAAQ,KAAK,GAAG;AAAA,MACjB;AAAA,IACD;AAEA,YAAQ,iBAAiB,KAAK;AAAA,EAC/B;AAEA,SAAO;AACR;AAOA,SAAS,2BAA2BA,UAAS,WAAW,OAAO;AAC9D,MAAI,CAAC,aAAK;AACV,MAAI,cAAc,YAAY,iBAAiBA,UAAS,KAAK,EAAG;AAChE,MAAI,cAAcA,SAAQ,aAAa,SAAS,KAAK,IAAI,KAAK,EAAG;AAEjE,EAAE;AAAA,IACD;AAAA,IACAA,SAAQ,UAAU,QAAQA,SAAQ,WAAWA,SAAQ,aAAa,KAAK;AAAA,IACvE,OAAO,KAAK;AAAA,EACb;AACD;AAOA,SAAS,cAAc,aAAa,KAAK;AACxC,MAAI,gBAAgB,IAAK,QAAO;AAChC,SAAO,IAAI,IAAI,aAAa,SAAS,OAAO,EAAE,SAAS,IAAI,IAAI,KAAK,SAAS,OAAO,EAAE;AACvF;AAGA,SAAS,aAAa,QAAQ;AAC7B,SAAO,OAAO,MAAM,GAAG,EAAE,IAAI,CAAC,QAAQ,IAAI,KAAK,EAAE,MAAM,GAAG,EAAE,OAAO,OAAO,CAAC;AAC5E;AAOA,SAAS,iBAAiBA,UAAS,QAAQ;AAC1C,MAAI,eAAe,aAAaA,SAAQ,MAAM;AAC9C,MAAI,OAAO,aAAa,MAAM;AAE9B,SACC,KAAK,WAAW,aAAa,UAC7B,KAAK;AAAA,IACJ,CAAC,CAAC,KAAK,KAAK,GAAG,MACd,UAAU,aAAa,CAAC,EAAE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,KAM1B,cAAc,aAAa,CAAC,EAAE,CAAC,GAAG,GAAG,KAAK,cAAc,KAAK,aAAa,CAAC,EAAE,CAAC,CAAC;AAAA,EAClF;AAEF;;;ACviBA,IAAM,MAAM,eAAU,MAAM,YAAY,IAAI,IAAI,MAAM,KAAK,IAAI;AAGxD,IAAM,MAAM;AAAA;AAAA;AAAA;AAAA,EAIlB;AAAA;AAAA,IAA4B,CAAC,OAAO,eAAU,wBAAwB,MAAM,CAAC;AAAA;AAAA,EAC7E,KAAK,MAAM,IAAI;AAAA,EACf,OAAO,oBAAI,IAAI;AAChB;;;ACPA,SAAS,YAAY;AAGpB,QAAMK,OAAM,IAAI,IAAI;AAEpB,MAAI,MAAM,QAAQ,CAAC,SAAS;AAC3B,QAAI,CAAC,KAAK,EAAEA,IAAG,GAAG;AACjB,UAAI,MAAM,OAAO,IAAI;AACrB,WAAK,EAAE;AAAA,IACR;AAAA,EACD,CAAC;AAED,MAAI,IAAI,MAAM,SAAS,GAAG;AACzB,QAAI,KAAK,SAAS;AAAA,EACnB;AACD;AAQO,SAAS,KAAK,UAAU;AAE9B,MAAI;AAEJ,MAAI,IAAI,MAAM,SAAS,GAAG;AACzB,QAAI,KAAK,SAAS;AAAA,EACnB;AAEA,SAAO;AAAA,IACN,SAAS,IAAI,QAAQ,CAAC,YAAY;AACjC,UAAI,MAAM,IAAK,OAAO,EAAE,GAAG,UAAU,GAAG,QAAQ,CAAE;AAAA,IACnD,CAAC;AAAA,IACD,QAAQ;AACP,UAAI,MAAM,OAAO,IAAI;AAAA,IACtB;AAAA,EACD;AACD;;;ACxBA,SAAS,eAAeC,UAAS,MAAM;AACtC,2BAAyB,MAAM;AAC9B,IAAAA,SAAQ,cAAc,IAAI,YAAY,IAAI,CAAC;AAAA,EAC5C,CAAC;AACF;AAOA,SAAS,0BAA0B,OAAO;AAEzC,MAAI,UAAU,QAAS,QAAO;AAC9B,MAAI,UAAU,SAAU,QAAO;AAG/B,MAAI,MAAM,WAAW,IAAI,EAAG,QAAO;AAEnC,QAAM,QAAQ,MAAM,MAAM,GAAG;AAC7B,MAAI,MAAM,WAAW,EAAG,QAAO,MAAM,CAAC;AACtC,SACC,MAAM,CAAC,IACP,MACE,MAAM,CAAC,EACP;AAAA;AAAA,IAA6B,CAAC,SAAS,KAAK,CAAC,EAAE,YAAY,IAAI,KAAK,MAAM,CAAC;AAAA,EAAC,EAC5E,KAAK,EAAE;AAEX;AAMA,SAAS,gBAAgB,KAAK;AAE7B,QAAM,WAAW,CAAC;AAClB,QAAM,QAAQ,IAAI,MAAM,GAAG;AAC3B,aAAW,QAAQ,OAAO;AACzB,UAAM,CAAC,UAAU,KAAK,IAAI,KAAK,MAAM,GAAG;AACxC,QAAI,CAAC,YAAY,UAAU,OAAW;AAEtC,UAAM,qBAAqB,0BAA0B,SAAS,KAAK,CAAC;AACpE,aAAS,kBAAkB,IAAI,MAAM,KAAK;AAAA,EAC3C;AACA,SAAO;AACR;AAGA,IAAM,SAAS,CAAC,MAAM;AAUf,SAAS,UAAUA,UAAS,QAAQ,YAAY;AACtD,MAAI;AAAA;AAAA,IAAgC;AAAA;AAGpC,MAAI;AAGJ,MAAI;AAGJ,MAAIC;AAGJ,MAAI,kBAAkB;AAEtB,OAAK,MAAL,KAAK,IAAM;AAAA,IACV,SAAAD;AAAA,IACA,UAAU;AACT,aAAO,KAAK,QAAQ,sBAAsB;AAAA,IAC3C;AAAA,IACA,QAAQ;AACP,MAAAC,cAAA,gBAAAA,WAAW;AAEX,WAAK,KAAK,QAAQ,sBAAsB;AAExC,UACC,KAAK,SAAS,GAAG,QACjB,KAAK,UAAU,GAAG,SAClB,KAAK,QAAQ,GAAG,OAChB,KAAK,WAAW,GAAG,QAClB;AACD,cAAM,UAAU,OAAO,EAAE,KAAK,SAAS,EAAE,MAAM,GAAG,GAAG,0CAAc;AAEnE,QAAAA,aAAY,QAAQ,KAAK,SAAS,SAAS,QAAW,GAAG,MAAM;AAC9D,UAAAA,cAAA,gBAAAA,WAAW;AACX,UAAAA,aAAY;AAAA,QACb,CAAC;AAAA,MACF;AAAA,IACD;AAAA,IACA,MAAM;AAKL,UAAID,SAAQ,cAAc,EAAE,OAAQ;AAIpC,UAAI,EAAE,UAAU,OAAO,OAAO,IAAI,iBAAiBA,QAAO;AAE1D,UAAI,aAAa,cAAc,aAAa,SAAS;AACpD,YAAI;AAAA;AAAA,UAAiDA,SAAS;AAAA;AAE9D,0BAAkB;AAAA,UACjB,UAAU,MAAM;AAAA,UAChB,OAAO,MAAM;AAAA,UACb,QAAQ,MAAM;AAAA,UACd,WAAW,MAAM;AAAA,QAClB;AAEA,cAAM,WAAW;AACjB,cAAM,QAAQ;AACd,cAAM,SAAS;AACf,YAAIE,MAAKF,SAAQ,sBAAsB;AAEvC,YAAI,KAAK,SAASE,IAAG,QAAQ,KAAK,QAAQA,IAAG,KAAK;AACjD,cAAI,YAAY,aAAa,KAAK,OAAOA,IAAG,IAAI,OAAO,KAAK,MAAMA,IAAG,GAAG;AACxE,gBAAM,YAAY,MAAM,YAAY,GAAG,MAAM,SAAS,IAAI,SAAS,KAAK;AAAA,QACzE;AAAA,MACD;AAAA,IACD;AAAA,IACA,QAAQ;AACP,UAAI,iBAAiB;AACpB,YAAI;AAAA;AAAA,UAAiDF,SAAS;AAAA;AAE9D,cAAM,WAAW,gBAAgB;AACjC,cAAM,QAAQ,gBAAgB;AAC9B,cAAM,SAAS,gBAAgB;AAC/B,cAAM,YAAY,gBAAgB;AAAA,MACnC;AAAA,IACD;AAAA,EACD;AAMA,OAAK,EAAE,UAAUA;AAClB;AAaO,SAAS,WAAW,OAAOA,UAAS,QAAQ,YAAY;AAC9D,MAAI,YAAY,QAAQ,mBAAmB;AAC3C,MAAI,YAAY,QAAQ,oBAAoB;AAC5C,MAAI,UAAU,YAAY;AAC1B,MAAI,aAAa,QAAQ,uBAAuB;AAGhD,MAAI,YAAY,UAAU,SAAS,WAAW,OAAO;AAGrD,MAAI;AAEJ,MAAI,QAAQA,SAAQ;AAOpB,MAAI,WAAWA,SAAQ,MAAM;AAG7B,MAAI;AAGJ,MAAI;AAEJ,WAAS,cAAc;AACtB,QAAI,oBAAoB;AACxB,QAAI,kBAAkB;AACtB,wBAAoB,IAAI;AACxB,sBAAkB,IAAI;AACtB,QAAI;AAIH,aAAQ,sCAAoB,OAAO,EAAEA,WAAS;AAAA,MAAoC,CAAC,GAAI;AAAA,QACtF;AAAA,MACD,CAAC;AAAA,IACF,UAAE;AACD,0BAAoB,iBAAiB;AACrC,wBAAkB,eAAe;AAAA,IAClC;AAAA,EACD;AAGA,MAAIG,cAAa;AAAA,IAChB;AAAA,IACA,KAAK;AAvOP;AAwOG,MAAAH,SAAQ,QAAQ;AAEhB,UAAI,CAAC,UAAU;AACd,uCAAO;AACP,6CAAO,UAAP;AACA;AAAA,MACD;AAEA,UAAI,CAAC,UAAU;AAGd,uCAAO;AAAA,MACR;AAEA,qBAAeA,UAAS,YAAY;AAEpC,cAAQ,QAAQA,UAAS,YAAY,GAAG,OAAO,GAAG,MAAM;AACvD,uBAAeA,UAAS,UAAU;AAGlC,uCAAO;AACP,gBAAQ,kBAAkB;AAE1B,QAAAA,SAAQ,MAAM,WAAW;AAAA,MAC1B,CAAC;AAAA,IACF;AAAA,IACA,IAAI,IAAI;AACP,UAAI,CAAC,UAAU;AACd;AACA,0BAAkB;AAClB;AAAA,MACD;AAEA,MAAAA,SAAQ,QAAQ;AAEhB,qBAAeA,UAAS,YAAY;AAEpC,cAAQ,QAAQA,UAAS,YAAY,GAAG,OAAO,GAAG,MAAM;AACvD,uBAAeA,UAAS,UAAU;AAClC;AAAA,MACD,CAAC;AAAA,IACF;AAAA,IACA,MAAM,MAAM;AACX,qCAAO;AACP,qCAAO;AAAA,IACR;AAAA,EACD;AAEA,MAAI;AAAA;AAAA,IAA2B;AAAA;AAE/B,GAAC,EAAE,gBAAF,EAAE,cAAgB,CAAC,IAAG,KAAKG,WAAU;AAKtC,MAAI,YAAY,cAAc;AAC7B,QAAIC,OAAM;AAEV,QAAI,CAACA,MAAK;AACT,UAAIC;AAAA;AAAA,QAAsC,EAAE;AAAA;AAG5C,aAAOA,WAAUA,OAAM,IAAI,wBAAwB,GAAG;AACrD,eAAQA,SAAQA,OAAM,QAAS;AAC9B,eAAKA,OAAM,IAAI,kBAAkB,EAAG;AAAA,QACrC;AAAA,MACD;AAEA,MAAAD,OAAM,CAACC,WAAUA,OAAM,IAAI,gBAAgB;AAAA,IAC5C;AAEA,QAAID,MAAK;AACR,aAAO,MAAM;AACZ,gBAAQ,MAAMD,YAAW,GAAG,CAAC;AAAA,MAC9B,CAAC;AAAA,IACF;AAAA,EACD;AACD;AAWA,SAAS,QAAQH,UAAS,SAAS,aAAa,IAAI,WAAW;AAC9D,MAAI,WAAW,OAAO;AAEtB,MAAI,YAAY,OAAO,GAAG;AAKzB,QAAI;AACJ,QAAI,UAAU;AAEd,qBAAiB,MAAM;AACtB,UAAI,QAAS;AACb,UAAI,IAAI,QAAQ,EAAE,WAAW,WAAW,OAAO,MAAM,CAAC;AACtD,UAAI,QAAQA,UAAS,GAAG,aAAa,IAAI,SAAS;AAAA,IACnD,CAAC;AAID,WAAO;AAAA,MACN,OAAO,MAAM;AACZ,kBAAU;AACV,+BAAG;AAAA,MACJ;AAAA,MACA,YAAY,MAAM,EAAE,WAAW;AAAA,MAC/B,OAAO,MAAM,EAAE,MAAM;AAAA,MACrB,GAAG,MAAM,EAAE,EAAE;AAAA,IACd;AAAA,EACD;AAEA,6CAAa;AAEb,MAAI,EAAC,mCAAS,WAAU;AACvB,cAAU;AAEV,WAAO;AAAA,MACN,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,GAAG,MAAM;AAAA,IACV;AAAA,EACD;AAEA,QAAM,EAAE,QAAQ,GAAG,KAAK,MAAAM,OAAM,SAAS,OAAO,IAAI;AAElD,MAAI,YAAY,CAAC;AAEjB,MAAI,YAAY,gBAAgB,QAAW;AAC1C,QAAIA,OAAM;AACT,MAAAA,MAAK,GAAG,CAAC;AAAA,IACV;AAEA,QAAI,KAAK;AACR,UAAI,SAAS,gBAAgB,IAAI,GAAG,CAAC,CAAC;AACtC,gBAAU,KAAK,QAAQ,MAAM;AAAA,IAC9B;AAAA,EACD;AAEA,MAAI,QAAQ,MAAM,IAAI;AAKtB,MAAIL,aAAYD,SAAQ,QAAQ,WAAW,EAAE,UAAU,MAAM,CAAC;AAE9D,EAAAC,WAAU,WAAW,MAAM;AAG1B,QAAI,MAAK,2CAAa,QAAO,IAAI;AACjC,+CAAa;AAEb,QAAI,QAAQ,KAAK;AACjB,QAAI;AAAA;AAAA,MAAkC,QAAQ,WAAY,KAAK,IAAI,KAAK;AAAA;AACxE,QAAIM,aAAY,CAAC;AAEjB,QAAI,WAAW,GAAG;AAMjB,UAAI,wBAAwB;AAE5B,UAAI,KAAK;AACR,YAAI,IAAI,KAAK,KAAK,YAAY,MAAO,GAAG;AAExC,iBAAS,IAAI,GAAG,KAAK,GAAG,KAAK,GAAG;AAC/B,cAAI,IAAI,KAAK,QAAQ,OAAO,IAAI,CAAC;AACjC,cAAIC,UAAS,gBAAgB,IAAI,GAAG,IAAI,CAAC,CAAC;AAC1C,UAAAD,WAAU,KAAKC,OAAM;AAErB,4DAA0BA,QAAO,aAAa;AAAA,QAC/C;AAAA,MACD;AAEA,UAAI,uBAAuB;AACC,QAACR,SAAS,MAAM,WAAW;AAAA,MACvD;AAEA,cAAQ,MAAM;AACb,YAAI;AAAA;AAAA;AAAA,UACkCC,WAAW;AAAA;AAGjD,eAAO,KAAK,QAAQ,OAAO,OAAO,QAAQ;AAAA,MAC3C;AAEA,UAAIK,OAAM;AACT,aAAK,MAAM;AACV,cAAIL,WAAU,cAAc,UAAW,QAAO;AAE9C,cAAIQ,KAAI,MAAM;AACd,UAAAH,MAAKG,IAAG,IAAIA,EAAC;AAEb,iBAAO;AAAA,QACR,CAAC;AAAA,MACF;AAAA,IACD;AAEA,IAAAR,aAAYD,SAAQ,QAAQO,YAAW,EAAE,UAAU,MAAM,WAAW,CAAC;AAErE,IAAAN,WAAU,WAAW,MAAM;AAC1B,cAAQ,MAAM;AACd,MAAAK,SAAA,gBAAAA,MAAO,IAAI,IAAI;AACf,gBAAU;AAAA,IACX;AAAA,EACD;AAEA,SAAO;AAAA,IACN,OAAO,MAAM;AACZ,UAAIL,YAAW;AACd,QAAAA,WAAU,OAAO;AAEjB,QAAAA,WAAU,SAAS;AAInB,QAAAA,WAAU,WAAW;AAAA,MACtB;AAAA,IACD;AAAA,IACA,YAAY,MAAM;AACjB,kBAAY;AAAA,IACb;AAAA,IACA,OAAO,MAAM;AACZ,UAAI,OAAO,GAAG;AACb,QAAAK,SAAA,gBAAAA,MAAO,GAAG;AAAA,MACX;AAAA,IACD;AAAA,IACA,GAAG,MAAM,MAAM;AAAA,EAChB;AACD;;;AChdO,SAAS,oBAAoBI,SAAQ;AAC3C,SAAO,UAAU,CAAC,WAAW,UAAU,GAAG,CAACC,WAAU;AACpD,QAAIA,UAASA,OAAM,SAAS;AAAA,IAAyCA,OAAO,eAAe;AAG1F;AAAA,IACD;AAEA,IAAAD,QAAO,SAAS,aAAa;AAAA,EAC9B,CAAC;AACF;;;ACAO,SAAS,WAAW,OAAOE,MAAKC,OAAMD,MAAK;AACjD,MAAI,QAAQ,SAAS;AAErB,kCAAgC,OAAO,SAAS,CAAC,aAAa;AAC7D,QAAI,gBAAO,MAAM,SAAS,YAAY;AAErC,MAAE,4BAA4B;AAAA,IAC/B;AAGA,QAAI,QAAQ,WAAW,MAAM,eAAe,MAAM;AAClD,YAAQ,oBAAoB,KAAK,IAAI,UAAU,KAAK,IAAI;AACxD,IAAAC,KAAI,KAAK;AAIT,QAAI,SAAS,WAAW,QAAQD,KAAI,IAAI;AACvC,UAAI,QAAQ,MAAM;AAClB,UAAI,MAAM,MAAM;AAGhB,YAAM,QAAQ,SAAS;AAGvB,UAAI,QAAQ,MAAM;AACjB,cAAM,iBAAiB;AACvB,cAAM,eAAe,KAAK,IAAI,KAAK,MAAM,MAAM,MAAM;AAAA,MACtD;AAAA,IACD;AAAA,EACD,CAAC;AAED;AAAA;AAAA;AAAA,IAGE,aAAa,MAAM,iBAAiB,MAAM;AAAA;AAAA,IAG1C,QAAQA,IAAG,KAAK,QAAQ,MAAM;AAAA,IAC9B;AACD,IAAAC,KAAI,oBAAoB,KAAK,IAAI,UAAU,MAAM,KAAK,IAAI,MAAM,KAAK;AAAA,EACtE;AAEA,gBAAc,MAAM;AACnB,QAAI,gBAAO,MAAM,SAAS,YAAY;AAErC,MAAE,4BAA4B;AAAA,IAC/B;AAEA,QAAI,QAAQD,KAAI;AAEhB,QAAI,oBAAoB,KAAK,KAAK,UAAU,UAAU,MAAM,KAAK,GAAG;AAEnE;AAAA,IACD;AAEA,QAAI,MAAM,SAAS,UAAU,CAAC,SAAS,CAAC,MAAM,OAAO;AAGpD;AAAA,IACD;AAIA,QAAI,UAAU,MAAM,OAAO;AAE1B,YAAM,QAAQ,SAAS;AAAA,IACxB;AAAA,EACD,CAAC;AACF;AAGA,IAAM,UAAU,oBAAI,IAAI;AAUjB,SAAS,WAAW,QAAQ,aAAa,OAAOA,MAAKC,OAAMD,MAAK;AACtE,MAAI,cAAc,MAAM,aAAa,MAAM,MAAM;AACjD,MAAI,gBAAgB;AAGpB,MAAIE,sBAAqB;AAEzB,MAAI,gBAAgB,MAAM;AACzB,aAASC,UAAS,aAAa;AAE9B,sBAAgB,cAAAA,YAAA,cAAAA,UAAyB,CAAC;AAAA,IAC3C;AAAA,EACD;AAEA,gBAAc,KAAK,KAAK;AAExB;AAAA,IACC;AAAA,IACA;AAAA,IACA,MAAM;AAEL,UAAI,QAAQ,MAAM;AAElB,UAAI,aAAa;AAChB,gBAAQ,wBAAwB,eAAe,OAAO,MAAM,OAAO;AAAA,MACpE;AAEA,MAAAF,KAAI,KAAK;AAAA,IACV;AAAA;AAAA,IAEA,MAAMA,KAAI,cAAc,CAAC,IAAI,IAAI;AAAA,EAClC;AAEA,gBAAc,MAAM;AACnB,QAAI,QAAQD,KAAI;AAIhB,QAAI,aAAa,MAAM,mBAAmB,MAAM,SAAS;AACxD,MAAAE,sBAAqB;AACrB;AAAA,IACD;AAEA,QAAI,aAAa;AAChB,cAAQ,SAAS,CAAC;AAElB,YAAM,UAAU,MAAM,SAAS,MAAM,OAAO;AAAA,IAC7C,OAAO;AAEN,YAAM,UAAU,GAAG,MAAM,SAAS,KAAK;AAAA,IACxC;AAAA,EACD,CAAC;AAED,WAAS,MAAM;AACd,QAAIC,SAAQ,cAAc,QAAQ,KAAK;AAEvC,QAAIA,WAAU,IAAI;AACjB,oBAAc,OAAOA,QAAO,CAAC;AAAA,IAC9B;AAAA,EACD,CAAC;AAED,MAAI,CAAC,QAAQ,IAAI,aAAa,GAAG;AAChC,YAAQ,IAAI,aAAa;AAEzB,qBAAiB,MAAM;AAEtB,oBAAc,KAAK,CAAC,GAAG,MAAO,EAAE,wBAAwB,CAAC,MAAM,IAAI,KAAK,CAAE;AAC1E,cAAQ,OAAO,aAAa;AAAA,IAC7B,CAAC;AAAA,EACF;AAEA,mBAAiB,MAAM;AACtB,QAAID,qBAAoB;AACvB,UAAI;AAEJ,UAAI,aAAa;AAChB,gBAAQ,wBAAwB,eAAe,OAAO,MAAM,OAAO;AAAA,MACpE,OAAO;AACN,YAAI,kBAAkB,cAAc,KAAK,CAACE,WAAUA,OAAM,OAAO;AAEjE,gBAAQ,mDAAiB;AAAA,MAC1B;AAEA,MAAAH,KAAI,KAAK;AAAA,IACV;AAAA,EACD,CAAC;AACF;AAQO,SAAS,aAAa,OAAOD,MAAKC,OAAMD,MAAK;AACnD,kCAAgC,OAAO,UAAU,CAAC,aAAa;AAC9D,QAAI,QAAQ,WAAW,MAAM,iBAAiB,MAAM;AACpD,IAAAC,KAAI,KAAK;AAAA,EACV,CAAC;AAED;AAAA;AAAA;AAAA,IAGE,aAAa,MAAM,mBAAmB,MAAM;AAAA,IAE7C,QAAQD,IAAG,KAAK;AAAA,IACf;AACD,IAAAC,KAAI,MAAM,OAAO;AAAA,EAClB;AAEA,gBAAc,MAAM;AACnB,QAAI,QAAQD,KAAI;AAChB,UAAM,UAAU,QAAQ,KAAK;AAAA,EAC9B,CAAC;AACF;AASA,SAAS,wBAAwB,OAAO,SAAS,SAAS;AACzD,MAAI,QAAQ,oBAAI,IAAI;AAEpB,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACzC,QAAI,MAAM,CAAC,EAAE,SAAS;AAErB,YAAM,IAAI,MAAM,CAAC,EAAE,OAAO;AAAA,IAC3B;AAAA,EACD;AAEA,MAAI,CAAC,SAAS;AACb,UAAM,OAAO,OAAO;AAAA,EACrB;AAEA,SAAO,MAAM,KAAK,KAAK;AACxB;AAKA,SAAS,oBAAoB,OAAO;AACnC,MAAI,OAAO,MAAM;AACjB,SAAO,SAAS,YAAY,SAAS;AACtC;AAKA,SAAS,UAAU,OAAO;AACzB,SAAO,UAAU,KAAK,OAAO,CAAC;AAC/B;AAOO,SAAS,WAAW,OAAOA,MAAKC,OAAMD,MAAK;AACjD,kCAAgC,OAAO,UAAU,MAAM;AACtD,IAAAC,KAAI,MAAM,KAAK;AAAA,EAChB,CAAC;AAED;AAAA;AAAA;AAAA,IAGC,aACA,MAAM;AAAA,IACL;AACD,IAAAA,KAAI,MAAM,KAAK;AAAA,EAChB;AAEA,gBAAc,MAAM;AACnB,UAAM,QAAQD,KAAI;AAAA,EACnB,CAAC;AACF;;;AC9QA,SAAS,qBAAqB,QAAQ;AACrC,MAAI,QAAQ,CAAC;AAEb,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AAC1C,UAAM,KAAK,EAAE,OAAO,OAAO,MAAM,CAAC,GAAG,KAAK,OAAO,IAAI,CAAC,EAAE,CAAC;AAAA,EAC1D;AAEA,SAAO;AACR;AAQO,SAAS,kBAAkB,OAAOK,MAAKC,OAAMD,MAAK;AAExD,MAAI;AAEJ,MAAI;AAKJ,MAAI,WAAW,MAAM;AACpB,yBAAqB,MAAM;AAE3B,QAAI,CAAC,MAAM,QAAQ;AAClB,eAAS,sBAAsB,QAAQ;AAAA,IACxC;AAEA,QAAI,aAAa,MAAM;AACvB,QAAI,UAAU,YAAY;AACzB,MAAAC,KAAK,QAAQ,UAAW;AAAA,IACzB;AAAA,EACD;AAEA,WAAS,sBAAsB,QAAQ;AACvC,QAAM,iBAAiB,cAAc,QAAQ;AAE7C,gBAAc,MAAM;AACnB,QAAI,aAAa,OAAOD,KAAI,CAAC;AAE7B,QAAI,UAAU,cAAc,CAAC;AAAA;AAAA,MAA0B;AAAA,IAAW,GAAG;AACpE,YAAM,cAAc,QAAQ;AAAA,IAC7B;AAAA,EACD,CAAC;AAED,WAAS,MAAM;AACd,yBAAqB,MAAM;AAC3B,UAAM,oBAAoB,cAAc,QAAQ;AAAA,EACjD,CAAC;AACF;AAMO,SAAS,cAAc,OAAOC,MAAK;AAEzC,MAAI;AAIJ,SAAO,OAAO,CAAC,kBAAkB,YAAY,cAAc,SAAS,GAAG,MAAM;AAC5E,QAAI,SAAS,MAAM;AAEnB,QACC,CAAC,WACD,QAAQ,WAAW,OAAO,UAC1B,QAAQ,KAAK,CAAC,OAAO,MAAM,OAAO,MAAM,CAAC,MAAM,MAAM,SAAS,OAAO,IAAI,CAAC,MAAM,MAAM,GAAG,GACxF;AACD,gBAAU,qBAAqB,MAAM;AACrC,MAAAA,KAAI,OAAO;AAAA,IACZ;AAAA,EACD,CAAC;AACF;AAMO,SAAS,cAAc,OAAOA,MAAK;AACzC,SAAO,OAAO,CAAC,gBAAgB,GAAG,MAAMA,KAAI,qBAAqB,MAAM,QAAQ,CAAC,CAAC;AAClF;AAMO,SAAS,YAAY,OAAOA,MAAK;AACvC,SAAO,OAAO,CAAC,YAAY,GAAG,MAAMA,KAAI,qBAAqB,MAAM,MAAM,CAAC,CAAC;AAC5E;AAMO,SAAS,aAAa,OAAOA,MAAK;AACxC,SAAO,OAAO,CAAC,WAAW,QAAQ,GAAG,MAAMA,KAAI,MAAM,OAAO,CAAC;AAC9D;AAMO,SAAS,WAAW,OAAOA,MAAK;AACtC,SAAO,OAAO,CAAC,cAAc,OAAO,GAAG,MAAMA,KAAI,MAAM,KAAK,CAAC;AAC9D;AAMO,SAAS,iBAAiB,OAAOA,MAAK;AAC5C;AAAA,IACC;AAAA,IACA,CAAC,kBAAkB,cAAc,WAAW,kBAAkB,WAAW,WAAW,SAAS;AAAA,IAC7F,MAAMA,KAAI,MAAM,UAAU;AAAA,EAC3B;AACD;AAOO,SAAS,mBAAmB,OAAOD,MAAKC,OAAMD,MAAK;AAGzD,SAAO,MAAM;AACZ,QAAI,QAAQ,OAAOA,KAAI,CAAC;AAExB,QAAI,UAAU,MAAM,gBAAgB,CAAC,MAAM,KAAK,GAAG;AAClD,YAAM,eAAe;AAAA,IACtB;AAAA,EACD,CAAC;AAID,SAAO,MAAM;AACZ,WAAO,OAAO,CAAC,YAAY,GAAG,MAAM;AACnC,MAAAC,KAAI,MAAM,YAAY;AAAA,IACvB,CAAC;AAAA,EACF,CAAC;AACF;AAOO,SAAS,YAAY,OAAOD,MAAKC,OAAMD,MAAK;AAClD,MAAI,SAASA,KAAI;AAEjB,MAAIE,UAAS,MAAM;AAClB,QAAI,WAAW,MAAM,QAAQ;AAC5B,MAAAD,KAAK,SAAS,MAAM,MAAO;AAAA,IAC5B;AAAA,EACD;AAIA,SAAO,OAAO,CAAC,QAAQ,SAAS,SAAS,GAAGC,SAAQ,UAAU,IAAI;AAIlE,SAAO,MAAM;AACZ,SAAK,SAAS,CAAC,CAACF,KAAI,OAAO,MAAM,QAAQ;AACxC,UAAI,QAAQ;AACX,cAAM,MAAM;AAAA,MACb,OAAO;AACN,cAAM,KAAK,EAAE,MAAM,MAAM;AACxB,UAAAC,KAAK,SAAS,IAAK;AAAA,QACpB,CAAC;AAAA,MACF;AAAA,IACD;AAAA,EACD,CAAC;AACF;AAOO,SAAS,YAAY,OAAOD,MAAKC,OAAMD,MAAK;AAClD,MAAI,WAAW,MAAM;AACpB,IAAAC,KAAI,MAAM,MAAM;AAAA,EACjB;AAEA,MAAID,KAAI,KAAK,MAAM;AAClB,aAAS;AAAA,EACV;AAEA,SAAO,OAAO,CAAC,cAAc,GAAG,UAAU,KAAK;AAE/C,gBAAc,MAAM;AACnB,QAAI,QAAQ,OAAOA,KAAI,CAAC;AAExB,QAAI,UAAU,MAAM,UAAU,CAAC,MAAM,KAAK,GAAG;AAC5C,YAAM,SAAS;AAAA,IAChB;AAAA,EACD,CAAC;AACF;AAOO,SAAS,WAAW,OAAOA,MAAKC,OAAMD,MAAK;AACjD,MAAI,WAAW,MAAM;AACpB,IAAAC,KAAI,MAAM,KAAK;AAAA,EAChB;AAEA,MAAID,KAAI,KAAK,MAAM;AAClB,aAAS;AAAA,EACV;AAEA,SAAO,OAAO,CAAC,cAAc,GAAG,UAAU,KAAK;AAE/C,gBAAc,MAAM;AACnB,QAAI,QAAQ,CAAC,CAACA,KAAI;AAElB,QAAI,MAAM,UAAU,MAAO,OAAM,QAAQ;AAAA,EAC1C,CAAC;AACF;;;ACjOO,SAAS,YAAYG,SAAQ;AACnC,SAAO,QAAQ,CAAC,UAAU,SAAS,GAAG,MAAM;AAC3C,IAAAA,QAAO,UAAU,MAAM;AAAA,EACxB,CAAC;AACF;;;ACEO,SAAS,UAAU,OAAOC,OAAM,OAAO;AAC7C,MAAI,OAAO,eAAe,OAAOA,KAAI;AAErC,MAAI,QAAQ,KAAK,KAAK;AACrB,UAAMA,KAAI,IAAI;AACd,aAAS,MAAM;AACd,YAAMA,KAAI,IAAI;AAAA,IACf,CAAC;AAAA,EACF;AACD;;;ACPO,SAAS,cAAc,QAAQ,OAAO,UAAU;AACtD,MAAI,OAAO,UAAU;AAEpB,QAAI,SAAS,QAAW;AACvB;AAAA,IACD;AAGA,QAAI,CAAC,SAAS,KAAK,GAAG;AACrB,aAAS,8BAA8B;AAAA,IACxC;AAGA,WAAO,eAAe,QAAQ,KAAK;AAAA,EACpC;AAEA,WAAS,UAAU,OAAO,SAAS;AAClC,QAAI,eAAe,iBAAiB,MAAM;AAC1C,QAAI,GAAG,cAAc,KAAK,GAAG;AAC5B,aAAO,WAAW;AAClB;AAAA,IACD;AAAA,EACD;AAEA,MAAI,CAAC,YAAY,UAAU,QAAW;AACrC,WAAO,gBAAgB;AAAA,EACxB;AACD;AAYO,SAAS,YAAY,QAAQ,WAAW;AAC9C,MAAI,WAAW;AACf,SAAO,MAAM;AACZ,QAAI,WAAW;AACd,oBAAc,QAAQ,QAAQ,SAAS,GAAG,QAAQ;AAAA,IACnD;AACA,eAAW;AAEX,QAAI,WAAW,IAAI,iBAAiB,MAAM;AAEzC,UAAI,QAAQ,OAAO;AACnB,oBAAc,QAAQ,KAAK;AAAA,IAG5B,CAAC;AAED,aAAS,QAAQ,QAAQ;AAAA;AAAA,MAExB,WAAW;AAAA,MACX,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,MAIT,YAAY;AAAA,MACZ,iBAAiB,CAAC,OAAO;AAAA,IAC1B,CAAC;AAED,WAAO,MAAM;AACZ,eAAS,WAAW;AAAA,IACrB;AAAA,EACD,CAAC;AACF;AAQO,SAAS,kBAAkB,QAAQC,MAAKC,OAAMD,MAAK;AACzD,MAAI,WAAW;AAEf,kCAAgC,QAAQ,UAAU,CAAC,aAAa;AAC/D,QAAI,QAAQ,WAAW,eAAe;AAEtC,QAAI;AAEJ,QAAI,OAAO,UAAU;AACpB,cAAQ,CAAC,EAAE,IAAI,KAAK,OAAO,iBAAiB,KAAK,GAAG,gBAAgB;AAAA,IACrE,OAAO;AAEN,UAAI,kBACH,OAAO,cAAc,KAAK;AAAA,MAE1B,OAAO,cAAc,wBAAwB;AAC9C,cAAQ,mBAAmB,iBAAiB,eAAe;AAAA,IAC5D;AAEA,IAAAC,KAAI,KAAK;AAAA,EACV,CAAC;AAGD,SAAO,MAAM;AACZ,QAAI,QAAQD,KAAI;AAChB,kBAAc,QAAQ,OAAO,QAAQ;AAGrC,QAAI,YAAY,UAAU,QAAW;AAEpC,UAAI,kBAAkB,OAAO,cAAc,UAAU;AACrD,UAAI,oBAAoB,MAAM;AAC7B,gBAAQ,iBAAiB,eAAe;AACxC,QAAAC,KAAI,KAAK;AAAA,MACV;AAAA,IACD;AAGA,WAAO,UAAU;AACjB,eAAW;AAAA,EACZ,CAAC;AAGD,cAAY,MAAM;AACnB;AAMA,SAAS,eAAe,QAAQ,OAAO;AACtC,WAAS,UAAU,OAAO,SAAS;AAClC,WAAO,WAAW,MAAM,SAAS,iBAAiB,MAAM,CAAC;AAAA,EAC1D;AACD;AAGA,SAAS,iBAAiB,QAAQ;AAEjC,MAAI,aAAa,QAAQ;AACxB,WAAO,OAAO;AAAA,EACf,OAAO;AACN,WAAO,OAAO;AAAA,EACf;AACD;;;AC5JA;AAQA,IAAM,2BAAN,MAAM,yBAAwB;AAAA;AAAA,EAc7B,YAAY,SAAS;AAdtB;AAEC;AAAA,mCAAa,oBAAI,QAAQ;AAGzB;AAAA;AAGA;AAAA;AAOC,uBAAK,UAAW;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQC,UAAS,UAAU;AAC1B,QAAI,YAAY,mBAAK,YAAW,IAAIA,QAAO,KAAK,oBAAI,IAAI;AACxD,cAAU,IAAI,QAAQ;AAEtB,uBAAK,YAAW,IAAIA,UAAS,SAAS;AACtC,0BAAK,oDAAL,WAAoB,QAAQA,UAAS,mBAAK,SAAQ;AAElD,WAAO,MAAM;AACZ,UAAIC,aAAY,mBAAK,YAAW,IAAID,QAAO;AAC3C,MAAAC,WAAU,OAAO,QAAQ;AAEzB,UAAIA,WAAU,SAAS,GAAG;AACzB,2BAAK,YAAW,OAAOD,QAAO;AACA,QAAC,mBAAK,WAAW,UAAUA,QAAO;AAAA,MACjE;AAAA,IACD;AAAA,EACD;AAiBD;AArDC;AAGA;AAGA;AARD;AAwCC,iBAAY,WAAG;AACd,SACC,mBAAK,cACJ,mBAAK,WAAY,IAAI;AAAA;AAAA,IACO,CAAC,YAAY;AACxC,eAAS,SAAS,SAAS;AAC1B,iCAAwB,QAAQ,IAAI,MAAM,QAAQ,KAAK;AACvD,iBAAS,YAAY,mBAAK,YAAW,IAAI,MAAM,MAAM,KAAK,CAAC,GAAG;AAC7D,mBAAS,KAAK;AAAA,QACf;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAEF;AAAA;AA3CA,cAXK,0BAWE,WAAU,oBAAI,QAAQ;AAX9B,IAAM,0BAAN;AAyDA,IAAI,8BAA8C,IAAI,wBAAwB;AAAA,EAC7E,KAAK;AACN,CAAC;AAED,IAAI,6BAA6C,IAAI,wBAAwB;AAAA,EAC5E,KAAK;AACN,CAAC;AAED,IAAI,2CAA2D,IAAI,wBAAwB;AAAA,EAC1F,KAAK;AACN,CAAC;AAOM,SAAS,qBAAqBA,UAAS,MAAME,MAAK;AACxD,MAAI,WACH,SAAS,iBAAiB,SAAS,mBAChC,8BACA,SAAS,kBACR,6BACA;AAEL,MAAI,QAAQ,SAAS;AAAA,IAAQF;AAAA;AAAA,IAAmC,CAAC,UAAUE,KAAI,MAAM,IAAI,CAAC;AAAA,EAAC;AAC3F,WAAS,KAAK;AACf;AAOO,SAAS,kBAAkBF,UAAS,MAAME,MAAK;AACrD,MAAI,QAAQ,2BAA2B,QAAQF,UAAS,MAAME,KAAIF,SAAQ,IAAI,CAAC,CAAC;AAEhF,SAAO,MAAM;AAEZ,YAAQ,MAAME,KAAIF,SAAQ,IAAI,CAAC,CAAC;AAChC,WAAO;AAAA,EACR,CAAC;AACF;;;ACjGA,SAAS,cAAc,aAAa,sBAAsB;AACzD,SACC,gBAAgB,yBAAwB,2CAAc,mBAAkB;AAE1E;AAUO,SAAS,UAAU,uBAAuB,CAAC,GAAGG,SAAQ,WAAW,WAAW;AAClF,SAAO,MAAM;AAEZ,QAAI;AAGJ,QAAI;AAEJ,kBAAc,MAAM;AACnB,kBAAY;AAEZ,eAAQ,6CAAiB,CAAC;AAE1B,cAAQ,MAAM;AACb,YAAI,yBAAyB,UAAU,GAAG,KAAK,GAAG;AACjD,UAAAA,QAAO,sBAAsB,GAAG,KAAK;AAGrC,cAAI,aAAa,cAAc,UAAU,GAAG,SAAS,GAAG,oBAAoB,GAAG;AAC9E,YAAAA,QAAO,MAAM,GAAG,SAAS;AAAA,UAC1B;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACF,CAAC;AAED,WAAO,MAAM;AAEZ,uBAAiB,MAAM;AACtB,YAAI,SAAS,cAAc,UAAU,GAAG,KAAK,GAAG,oBAAoB,GAAG;AACtE,UAAAA,QAAO,MAAM,GAAG,KAAK;AAAA,QACtB;AAAA,MACD,CAAC;AAAA,IACF;AAAA,EACD,CAAC;AAED,SAAO;AACR;;;AClDO,SAAS,sBAAsB,UAAUC,UAASC,MAAKC,OAAMD,MAAK;AACxE,EAAAD,SAAQ,iBAAiB,SAAS,MAAM;AAEvC,IAAAE,KAAIF,SAAQ,QAAQ,CAAC;AAAA,EACtB,CAAC;AAED,gBAAc,MAAM;AACnB,QAAI,QAAQC,KAAI;AAEhB,QAAID,SAAQ,QAAQ,MAAM,OAAO;AAChC,UAAI,SAAS,MAAM;AAElB,YAAI,iBAAiBA,SAAQ,QAAQ;AACrC,QAAAE,KAAI,cAAc;AAAA,MACnB,OAAO;AAEN,QAAAF,SAAQ,QAAQ,IAAI,QAAQ;AAAA,MAC7B;AAAA,IACD;AAAA,EACD,CAAC;AACF;AAUO,SAAS,cAAc,UAAU,YAAYA,UAASE,MAAKD,MAAK;AACtE,MAAI,UAAU,MAAM;AAEnB,IAAAC,KAAIF,SAAQ,QAAQ,CAAC;AAAA,EACtB;AAEA,EAAAA,SAAQ,iBAAiB,YAAY,OAAO;AAE5C,MAAIC,MAAK;AACR,kBAAc,MAAM;AAEnB,MAAAD,SAAQ,QAAQ,IAAIC,KAAI;AAAA,IACzB,CAAC;AAAA,EACF,OAAO;AACN,YAAQ;AAAA,EACT;AAGA,MAAID,aAAY,SAAS,QAAQA,aAAY,UAAUA,aAAY,UAAU;AAC5E,aAAS,MAAM;AACd,MAAAA,SAAQ,oBAAoB,YAAY,OAAO;AAAA,IAChD,CAAC;AAAA,EACF;AACD;AAOO,SAAS,aAAaA,UAASE,MAAK;AAC1C,SAAOF,UAAS,CAAC,SAAS,MAAM,GAAG,MAAM;AACxC,IAAAE,KAAIF,aAAY,SAAS,aAAa;AAAA,EACvC,CAAC;AACF;;;ACjEO,SAAS,mBAAmB,MAAMG,MAAKC,OAAMD,MAAK;AACxD,MAAI,iBAAiB,SAAS;AAE9B,MAAI,iBAAiB,MACpB,yBAAyB,MAAM;AAC9B,gBAAY;AACZ,iBAAa,OAAO;AACpB,cAAU,WAAW,OAAO,GAAG;AAE/B,IAAAC,KAAI,OAAO,iBAAiB,YAAY,SAAS,CAAC;AAAA,EACnD,CAAC;AAEF,mBAAiB,UAAU,gBAAgB;AAAA,IAC1C,SAAS;AAAA,EACV,CAAC;AAED,MAAI,YAAY;AAGhB,MAAI;AACJ,MAAI,QAAQ,MAAM;AACjB,gBAAY;AAAA,EACb;AACA,MAAI,QAAQ;AAEZ,gBAAc,MAAM;AACnB,QAAI,eAAeD,KAAI;AAEvB,QAAI,OAAO;AACV,cAAQ;AAAA,IACT,WAAW,CAAC,aAAa,gBAAgB,MAAM;AAC9C,kBAAY;AACZ,mBAAa,OAAO;AACpB,UAAI,gBAAgB;AACnB,iBAAS,cAAc,OAAO,OAAO;AAAA,MACtC,OAAO;AACN,iBAAS,OAAO,SAAS,YAAY;AAAA,MACtC;AACA,gBAAU,WAAW,OAAO,GAAG;AAAA,IAChC;AAAA,EACD,CAAC;AAGD,SAAO,cAAc;AAErB,WAAS,MAAM;AACd,wBAAoB,UAAU,cAAc;AAAA,EAC7C,CAAC;AACF;AAMO,SAAS,iBAAiB,MAAMC,MAAK;AAC3C,SAAO,QAAQ,CAAC,QAAQ,GAAG,MAAM,yBAAyB,MAAMA,KAAI,OAAO,IAAI,CAAC,CAAC,CAAC;AACnF;;;ACtDO,SAAS,KAAK,YAAY,OAAO;AACvC,QAAM;AAAA;AAAA,IAAiD;AAAA;AAEvD,QAAM,YAAY,QAAQ,EAAE;AAC5B,MAAI,CAAC,UAAW;AAEhB,MAAI,QAAQ,MAAM,gBAAgB,QAAQ,CAAC;AAE3C,MAAI,WAAW;AACd,QAAI,UAAU;AACd,QAAI;AAAA;AAAA,MAA2C,CAAC;AAAA;AAGhD,UAAM,IAAI,QAAQ,MAAM;AACvB,UAAI,UAAU;AACd,YAAMC,SAAQ,QAAQ;AACtB,iBAAW,OAAOA,QAAO;AACxB,YAAIA,OAAM,GAAG,MAAM,KAAK,GAAG,GAAG;AAC7B,eAAK,GAAG,IAAIA,OAAM,GAAG;AACrB,oBAAU;AAAA,QACX;AAAA,MACD;AACA,UAAI,QAAS;AACb,aAAO;AAAA,IACR,CAAC;AAED,YAAQ,MAAM,IAAI,CAAC;AAAA,EACpB;AAGA,MAAI,UAAU,EAAE,QAAQ;AACvB,oBAAgB,MAAM;AACrB,kBAAY,SAAS,KAAK;AAC1B,cAAQ,UAAU,CAAC;AAAA,IACpB,CAAC;AAAA,EACF;AAGA,cAAY,MAAM;AACjB,UAAM,MAAM,QAAQ,MAAM,UAAU,EAAE,IAAI,GAAG,CAAC;AAC9C,WAAO,MAAM;AACZ,iBAAW,MAAM,KAAK;AACrB,YAAI,OAAO,OAAO,YAAY;AAC7B,aAAG;AAAA,QACJ;AAAA,MACD;AAAA,IACD;AAAA,EACD,CAAC;AAGD,MAAI,UAAU,EAAE,QAAQ;AACvB,gBAAY,MAAM;AACjB,kBAAY,SAAS,KAAK;AAC1B,cAAQ,UAAU,CAAC;AAAA,IACpB,CAAC;AAAA,EACF;AACD;AAQA,SAAS,YAAY,SAAS,OAAO;AACpC,MAAI,QAAQ,EAAE,GAAG;AAChB,eAAW,UAAU,QAAQ,EAAE,EAAG,KAAI,MAAM;AAAA,EAC7C;AAEA,QAAM;AACP;;;ACxEO,SAAS,gBAAgB,IAAI;AACnC,MAAI,IAAI,OAAO,CAAC;AAEhB,SAAO,WAAY;AAClB,QAAI,UAAU,WAAW,GAAG;AAC3B,UAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACjB,aAAO,UAAU,CAAC;AAAA,IACnB,OAAO;AACN,UAAI,CAAC;AACL,aAAO,GAAG;AAAA,IACX;AAAA,EACD;AACD;AAQO,SAAS,aAAa,SAASC,QAAO;AA7B7C;AA8BC,MAAI;AAAA;AAAA,KAA+D,aAAQ,aAAR,mBAClEA,OAAM;AAAA;AAGP,MAAI,YAAY,SAAS,MAAM,IAAI,OAAO,MAAM,IAAI,UAAU,OAAO,CAAC,IAAI,CAAC,MAAM;AAEjF,WAAS,MAAM,WAAW;AAEzB,OAAG,KAAK,MAAMA,MAAK;AAAA,EACpB;AACD;AAQO,SAAS,0BAA0B,SAAS,YAAY,gBAAgB;AAhD/E;AAiDC,UAAQ,aAAR,QAAQ,WAAa,CAAC;AACtB,gBAAQ,UAAR,iCAAiC,CAAC;AAClC,UAAQ,SAAS,UAAU,EAAE,KAAK,cAAc;AACjD;AASO,SAAS,oBAAoB,aAAa;AAChD,WAAS,OAAO,aAAa;AAC5B,QAAI,OAAO,MAAM;AAChB,WAAK,GAAG,IAAI,YAAY,GAAG;AAAA,IAC5B;AAAA,EACD;AACD;;;ACvDA,IAAI,cAAK;AAIR,MAAS,mBAAT,SAA0B,MAAM;AAC/B,QAAI,EAAE,QAAQ,aAAa;AAG1B,UAAI;AACJ,aAAO,eAAe,YAAY,MAAM;AAAA,QACvC,cAAc;AAAA;AAAA,QAEd,KAAK,MAAM;AACV,cAAI,UAAU,QAAW;AACxB,mBAAO;AAAA,UACR;AAEA,UAAE,oBAAoB,IAAI;AAAA,QAC3B;AAAA,QACA,KAAK,CAAC,MAAM;AACX,kBAAQ;AAAA,QACT;AAAA,MACD,CAAC;AAAA,IACF;AAAA,EACD;AAEA,mBAAiB,QAAQ;AACzB,mBAAiB,SAAS;AAC1B,mBAAiB,UAAU;AAC3B,mBAAiB,UAAU;AAC3B,mBAAiB,QAAQ;AACzB,mBAAiB,WAAW;AAC7B;AAgBO,SAAS,QAAQ,IAAI;AAC3B,MAAI,sBAAsB,MAAM;AAC/B,gCAA4B,SAAS;AAAA,EACtC;AAEA,MAAI,oBAAoB,kBAAkB,MAAM,MAAM;AACrD,0BAAsB,iBAAiB,EAAE,EAAE,KAAK,EAAE;AAAA,EACnD,OAAO;AACN,gBAAY,MAAM;AACjB,YAAM,UAAU,QAAQ,EAAE;AAC1B,UAAI,OAAO,YAAY,WAAY;AAAA;AAAA,QAAkC;AAAA;AAAA,IACtE,CAAC;AAAA,EACF;AACD;AAWO,SAAS,UAAU,IAAI;AAC7B,MAAI,sBAAsB,MAAM;AAC/B,gCAA4B,WAAW;AAAA,EACxC;AAEA,UAAQ,MAAM,MAAM,QAAQ,EAAE,CAAC;AAChC;AASA,SAAS,oBAAoB,MAAM,QAAQ,EAAE,UAAU,OAAO,aAAa,MAAM,IAAI,CAAC,GAAG;AACxF,SAAO,IAAI,YAAY,MAAM,EAAE,QAAQ,SAAS,WAAW,CAAC;AAC7D;AAyBO,SAAS,wBAAwB;AACvC,QAAM,2BAA2B;AACjC,MAAI,6BAA6B,MAAM;AACtC,gCAA4B,uBAAuB;AAAA,EACpD;AAEA,SAAO,CAAC,MAAM,QAAQ,YAAY;AApInC;AAqIE,UAAM;AAAA;AAAA,OACL,8BAAyB,EAAE,aAA3B;AAAA;AAAA,QACuB;AAAA;AAAA;AAExB,QAAI,QAAQ;AACX,YAAM,YAAY,SAAS,MAAM,IAAI,OAAO,MAAM,IAAI,CAAC,MAAM;AAG7D,YAAMC,SAAQ;AAAA;AAAA,QAA2C;AAAA,QAAO;AAAA,QAAQ;AAAA,MAAO;AAC/E,iBAAW,MAAM,WAAW;AAC3B,WAAG,KAAK,yBAAyB,GAAGA,MAAK;AAAA,MAC1C;AACA,aAAO,CAACA,OAAM;AAAA,IACf;AAEA,WAAO;AAAA,EACR;AACD;AAeO,SAAS,aAAa,IAAI;AAChC,MAAI,sBAAsB,MAAM;AAC/B,gCAA4B,cAAc;AAAA,EAC3C;AAEA,MAAI,kBAAkB,MAAM,MAAM;AACjC,IAAE,sBAAsB,cAAc;AAAA,EACvC;AAEA,wBAAsB,iBAAiB,EAAE,EAAE,KAAK,EAAE;AACnD;AAaO,SAAS,YAAY,IAAI;AAC/B,MAAI,sBAAsB,MAAM;AAC/B,gCAA4B,aAAa;AAAA,EAC1C;AAEA,MAAI,kBAAkB,MAAM,MAAM;AACjC,IAAE,sBAAsB,aAAa;AAAA,EACtC;AAEA,wBAAsB,iBAAiB,EAAE,EAAE,KAAK,EAAE;AACnD;AAMA,SAAS,sBAAsB,SAAS;AACvC,MAAI;AAAA;AAAA,IAA2C,QAAS;AAAA;AACxD,SAAQ,EAAE,MAAF,EAAE,IAAM,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,EAAE;AACvC;;;ACpMO,SAAS,mBAAmB,OAAOC,MAAK,YAAY;AAC1D,MAAI,SAAS,MAAM;AAElB,IAAAA,KAAI,MAAS;AAGb,QAAI,WAAY,YAAW,MAAS;AAEpC,WAAO;AAAA,EACR;AAIA,QAAM,QAAQ;AAAA,IAAQ,MACrB,MAAM;AAAA,MACLA;AAAA;AAAA,MAEA;AAAA,IACD;AAAA,EACD;AAIA,SAAO,MAAM,cAAc,MAAM,MAAM,YAAY,IAAI;AACxD;;;AC1BA,IAAM,mBAAmB,CAAC;AAUnB,SAAS,SAAS,OAAO,OAAO;AACtC,SAAO;AAAA,IACN,WAAW,SAAS,OAAO,KAAK,EAAE;AAAA,EACnC;AACD;AAUO,SAAS,SAAS,OAAO,QAAQ,MAAM;AAE7C,MAAI,OAAO;AAGX,QAAM,cAAc,oBAAI,IAAI;AAM5B,WAASC,KAAI,WAAW;AACvB,QAAI,eAAe,OAAO,SAAS,GAAG;AACrC,cAAQ;AACR,UAAI,MAAM;AAET,cAAM,YAAY,CAAC,iBAAiB;AACpC,mBAAW,cAAc,aAAa;AACrC,qBAAW,CAAC,EAAE;AACd,2BAAiB,KAAK,YAAY,KAAK;AAAA,QACxC;AACA,YAAI,WAAW;AACd,mBAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK,GAAG;AACpD,6BAAiB,CAAC,EAAE,CAAC,EAAE,iBAAiB,IAAI,CAAC,CAAC;AAAA,UAC/C;AACA,2BAAiB,SAAS;AAAA,QAC3B;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAMA,WAASC,QAAO,IAAI;AACnB,IAAAD,KAAI;AAAA;AAAA,MAAqB;AAAA,IAAM,CAAC;AAAA,EACjC;AAOA,WAAS,UAAUE,MAAK,aAAa,MAAM;AAE1C,UAAM,aAAa,CAACA,MAAK,UAAU;AACnC,gBAAY,IAAI,UAAU;AAC1B,QAAI,YAAY,SAAS,GAAG;AAC3B,aAAO,MAAMF,MAAKC,OAAM,KAAK;AAAA,IAC9B;AACA,IAAAC;AAAA;AAAA,MAAsB;AAAA,IAAM;AAC5B,WAAO,MAAM;AACZ,kBAAY,OAAO,UAAU;AAC7B,UAAI,YAAY,SAAS,KAAK,MAAM;AACnC,aAAK;AACL,eAAO;AAAA,MACR;AAAA,IACD;AAAA,EACD;AACA,SAAO,EAAE,KAAAF,MAAK,QAAAC,SAAQ,UAAU;AACjC;AAkCO,SAASE,SAAQ,QAAQ,IAAI,eAAe;AAClD,QAAM,SAAS,CAAC,MAAM,QAAQ,MAAM;AAEpC,QAAM,eAAe,SAAS,CAAC,MAAM,IAAI;AACzC,MAAI,CAAC,aAAa,MAAM,OAAO,GAAG;AACjC,UAAM,IAAI,MAAM,sDAAsD;AAAA,EACvE;AACA,QAAM,OAAO,GAAG,SAAS;AACzB,SAAO,SAAS,eAAe,CAACH,MAAKC,YAAW;AAC/C,QAAI,UAAU;AAEd,UAAM,SAAS,CAAC;AAChB,QAAIG,WAAU;AACd,QAAI,UAAU;AACd,UAAM,OAAO,MAAM;AAClB,UAAIA,UAAS;AACZ;AAAA,MACD;AACA,cAAQ;AACR,YAAM,SAAS,GAAG,SAAS,OAAO,CAAC,IAAI,QAAQJ,MAAKC,OAAM;AAC1D,UAAI,MAAM;AACT,QAAAD,KAAI,MAAM;AAAA,MACX,OAAO;AACN,kBAAU,OAAO,WAAW,aAAa,SAAS;AAAA,MACnD;AAAA,IACD;AACA,UAAM,gBAAgB,aAAa;AAAA,MAAI,CAAC,OAAO,MAC9C;AAAA,QACC;AAAA,QACA,CAAC,UAAU;AACV,iBAAO,CAAC,IAAI;AACZ,UAAAI,YAAW,EAAE,KAAK;AAClB,cAAI,SAAS;AACZ,iBAAK;AAAA,UACN;AAAA,QACD;AAAA,QACA,MAAM;AACL,UAAAA,YAAW,KAAK;AAAA,QACjB;AAAA,MACD;AAAA,IACD;AACA,cAAU;AACV,SAAK;AACL,WAAO,SAAS,OAAO;AACtB,cAAQ,aAAa;AACrB,cAAQ;AAIR,gBAAU;AAAA,IACX;AAAA,EACD,CAAC;AACF;AASO,SAAS,SAAS,OAAO;AAC/B,SAAO;AAAA;AAAA,IAEN,WAAW,MAAM,UAAU,KAAK,KAAK;AAAA,EACtC;AACD;AASO,SAASC,KAAI,OAAO;AAC1B,MAAI;AACJ,qBAAmB,OAAO,CAAC,MAAO,QAAQ,CAAE,EAAE;AAE9C,SAAO;AACR;;;AClMA,IAAI,mBAAmB;AAEvB,IAAI,eAAe,OAAO;AAYnB,SAAS,UAAU,OAAO,YAAY,QAAQ;AACpD,QAAM,QAAS,4CAAuB;AAAA,IACrC,OAAO;AAAA,IACP,QAAQ,eAAe,MAAS;AAAA,IAChC,aAAa;AAAA,EACd;AAGA,MAAI,MAAM,UAAU,SAAS,EAAE,gBAAgB,SAAS;AACvD,UAAM,YAAY;AAClB,UAAM,QAAQ,SAAS;AAEvB,QAAI,SAAS,MAAM;AAClB,YAAM,OAAO,IAAI;AACjB,YAAM,cAAc;AAAA,IACrB,OAAO;AACN,UAAI,0BAA0B;AAE9B,YAAM,cAAc,mBAAmB,OAAO,CAAC,MAAM;AACpD,YAAI,yBAAyB;AAG5B,gBAAM,OAAO,IAAI;AAAA,QAClB,OAAO;AACN,cAAI,MAAM,QAAQ,CAAC;AAAA,QACpB;AAAA,MACD,CAAC;AAED,gCAA0B;AAAA,IAC3B;AAAA,EACD;AAKA,MAAI,SAAS,gBAAgB,QAAQ;AACpC,WAAOC,KAAU,KAAK;AAAA,EACvB;AAEA,SAAO,IAAI,MAAM,MAAM;AACxB;AAUO,SAAS,YAAY,OAAO,YAAY,QAAQ;AAEtD,MAAI,QAAQ,OAAO,UAAU;AAE7B,MAAI,SAAS,MAAM,UAAU,OAAO;AAEnC,UAAM,YAAY;AAClB,UAAM,cAAc;AAAA,EACrB;AAEA,SAAO;AACR;AASO,SAAS,UAAU,OAAO,OAAO;AACvC,QAAM,IAAI,KAAK;AACf,SAAO;AACR;AAMO,SAAS,iBAAiB,QAAQ,YAAY;AACpD,MAAI,QAAQ,OAAO,UAAU;AAC7B,MAAI,MAAM,UAAU,MAAM;AACzB,cAAU,MAAM,OAAO,MAAM,OAAO,CAAC;AAAA,EACtC;AACD;AAMO,SAAS,eAAe;AAE9B,QAAM,SAAS,CAAC;AAEhB,WAAS,UAAU;AAClB,aAAS,MAAM;AACd,eAAS,cAAc,QAAQ;AAC9B,cAAM,MAAM,OAAO,UAAU;AAC7B,YAAI,YAAY;AAAA,MACjB;AACA,sBAAgB,QAAQ,cAAc;AAAA,QACrC,YAAY;AAAA,QACZ,OAAO;AAAA,MACR,CAAC;AAAA,IACF,CAAC;AAAA,EACF;AAEA,SAAO,CAAC,QAAQ,OAAO;AACxB;AASO,SAAS,aAAa,OAAO,YAAY,WAAW;AAC1D,QAAM,IAAI,SAAS;AACnB,SAAO;AACR;AAQO,SAAS,aAAa,OAAO,aAAa,IAAI,GAAG;AACvD,QAAM,IAAI,cAAc,CAAC;AACzB,SAAO;AACR;AAQO,SAAS,iBAAiB,OAAO,aAAa,IAAI,GAAG;AAC3D,QAAM,QAAQ,cAAc;AAC5B,QAAM,IAAI,KAAK;AACf,SAAO;AACR;AAKO,SAAS,qBAAqB;AACpC,qBAAmB;AACpB;AAUO,SAAS,sBAAsB,IAAI;AACzC,MAAI,4BAA4B;AAEhC,MAAI;AACH,uBAAmB;AACnB,WAAO,CAAC,GAAG,GAAG,gBAAgB;AAAA,EAC/B,UAAE;AACD,uBAAmB;AAAA,EACpB;AACD;;;AC5KO,SAAS,YAAY,IAAI,IAAI,GAAG;AACtC,QAAM,QAAQ,GAAG;AACjB,KAAG,QAAQ,CAAC;AACZ,SAAO;AACR;AAOO,SAAS,gBAAgB,IAAI,IAAI,GAAG;AAC1C,QAAM,QAAQ,GAAG,IAAI;AACrB,KAAG,KAAK;AACR,SAAO;AACR;AAOA,IAAM,qBAAqB;AAAA,EAC1B,IAAI,QAAQ,KAAK;AAChB,QAAI,OAAO,QAAQ,SAAS,GAAG,EAAG;AAClC,WAAO,OAAO,MAAM,GAAG;AAAA,EACxB;AAAA,EACA,IAAI,QAAQ,KAAK;AAChB,QAAI,cAAK;AAER,MAAE,oBAAoB,GAAG,OAAO,IAAI,IAAI,OAAO,GAAG,CAAC,EAAE;AAAA,IACtD;AAEA,WAAO;AAAA,EACR;AAAA,EACA,yBAAyB,QAAQ,KAAK;AACrC,QAAI,OAAO,QAAQ,SAAS,GAAG,EAAG;AAClC,QAAI,OAAO,OAAO,OAAO;AACxB,aAAO;AAAA,QACN,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,OAAO,OAAO,MAAM,GAAG;AAAA,MACxB;AAAA,IACD;AAAA,EACD;AAAA,EACA,IAAI,QAAQ,KAAK;AAChB,QAAI,OAAO,QAAQ,SAAS,GAAG,EAAG,QAAO;AACzC,WAAO,OAAO,OAAO;AAAA,EACtB;AAAA,EACA,QAAQ,QAAQ;AACf,WAAO,QAAQ,QAAQ,OAAO,KAAK,EAAE,OAAO,CAAC,QAAQ,CAAC,OAAO,QAAQ,SAAS,GAAG,CAAC;AAAA,EACnF;AACD;AASO,SAAS,WAAW,OAAO,SAAS,MAAM;AAChD,SAAO,IAAI;AAAA,IACV,eAAM,EAAE,OAAO,SAAS,MAAM,OAAO,CAAC,GAAG,UAAU,CAAC,EAAE,IAAI,EAAE,OAAO,QAAQ;AAAA,IAC3E;AAAA,EACD;AACD;AAMA,IAAM,4BAA4B;AAAA,EACjC,IAAI,QAAQ,KAAK;AAChB,QAAI,OAAO,QAAQ,SAAS,GAAG,EAAG;AAClC,QAAI,OAAO,OAAO;AAClB,WAAO,OAAO,OAAO,UAAU,OAAO,QAAQ,GAAG,EAAE,IAAI,OAAO,MAAM,GAAG;AAAA,EACxE;AAAA,EACA,IAAI,QAAQ,KAAK,OAAO;AACvB,QAAI,EAAE,OAAO,OAAO,UAAU;AAG7B,aAAO,QAAQ,GAAG,IAAI;AAAA,QACrB;AAAA,UACC,KAAK,GAAG,IAAI;AACX,mBAAO,OAAO,MAAM,GAAG;AAAA,UACxB;AAAA,QACD;AAAA;AAAA,QACuB;AAAA,QACvB;AAAA,MACD;AAAA,IACD;AAEA,WAAO,QAAQ,GAAG,EAAE,KAAK;AACzB,WAAO,OAAO,OAAO;AACrB,WAAO;AAAA,EACR;AAAA,EACA,yBAAyB,QAAQ,KAAK;AACrC,QAAI,OAAO,QAAQ,SAAS,GAAG,EAAG;AAClC,QAAI,OAAO,OAAO,OAAO;AACxB,aAAO;AAAA,QACN,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,OAAO,OAAO,MAAM,GAAG;AAAA,MACxB;AAAA,IACD;AAAA,EACD;AAAA,EACA,eAAe,QAAQ,KAAK;AAE3B,QAAI,OAAO,QAAQ,SAAS,GAAG,EAAG,QAAO;AACzC,WAAO,QAAQ,KAAK,GAAG;AACvB,WAAO,OAAO,OAAO;AACrB,WAAO;AAAA,EACR;AAAA,EACA,IAAI,QAAQ,KAAK;AAChB,QAAI,OAAO,QAAQ,SAAS,GAAG,EAAG,QAAO;AACzC,WAAO,OAAO,OAAO;AAAA,EACtB;AAAA,EACA,QAAQ,QAAQ;AACf,WAAO,QAAQ,QAAQ,OAAO,KAAK,EAAE,OAAO,CAAC,QAAQ,CAAC,OAAO,QAAQ,SAAS,GAAG,CAAC;AAAA,EACnF;AACD;AAOO,SAAS,kBAAkB,OAAO,SAAS;AACjD,SAAO,IAAI,MAAM,EAAE,OAAO,SAAS,SAAS,CAAC,GAAG,SAAS,OAAO,CAAC,EAAE,GAAG,yBAAyB;AAChG;AASA,IAAM,uBAAuB;AAAA,EAC5B,IAAI,QAAQ,KAAK;AAChB,QAAI,IAAI,OAAO,MAAM;AACrB,WAAO,KAAK;AACX,UAAI,IAAI,OAAO,MAAM,CAAC;AACtB,UAAI,YAAY,CAAC,EAAG,KAAI,EAAE;AAC1B,UAAI,OAAO,MAAM,YAAY,MAAM,QAAQ,OAAO,EAAG,QAAO,EAAE,GAAG;AAAA,IAClE;AAAA,EACD;AAAA,EACA,IAAI,QAAQ,KAAK,OAAO;AACvB,QAAI,IAAI,OAAO,MAAM;AACrB,WAAO,KAAK;AACX,UAAI,IAAI,OAAO,MAAM,CAAC;AACtB,UAAI,YAAY,CAAC,EAAG,KAAI,EAAE;AAC1B,YAAM,OAAO,eAAe,GAAG,GAAG;AAClC,UAAI,QAAQ,KAAK,KAAK;AACrB,aAAK,IAAI,KAAK;AACd,eAAO;AAAA,MACR;AAAA,IACD;AACA,WAAO;AAAA,EACR;AAAA,EACA,yBAAyB,QAAQ,KAAK;AACrC,QAAI,IAAI,OAAO,MAAM;AACrB,WAAO,KAAK;AACX,UAAI,IAAI,OAAO,MAAM,CAAC;AACtB,UAAI,YAAY,CAAC,EAAG,KAAI,EAAE;AAC1B,UAAI,OAAO,MAAM,YAAY,MAAM,QAAQ,OAAO,GAAG;AACpD,cAAM,aAAa,eAAe,GAAG,GAAG;AACxC,YAAI,cAAc,CAAC,WAAW,cAAc;AAI3C,qBAAW,eAAe;AAAA,QAC3B;AACA,eAAO;AAAA,MACR;AAAA,IACD;AAAA,EACD;AAAA,EACA,IAAI,QAAQ,KAAK;AAEhB,QAAI,QAAQ,gBAAgB,QAAQ,aAAc,QAAO;AAEzD,aAAS,KAAK,OAAO,OAAO;AAC3B,UAAI,YAAY,CAAC,EAAG,KAAI,EAAE;AAC1B,UAAI,KAAK,QAAQ,OAAO,EAAG,QAAO;AAAA,IACnC;AAEA,WAAO;AAAA,EACR;AAAA,EACA,QAAQ,QAAQ;AAEf,UAAM,OAAO,CAAC;AAEd,aAAS,KAAK,OAAO,OAAO;AAC3B,UAAI,YAAY,CAAC,EAAG,KAAI,EAAE;AAC1B,UAAI,CAAC,EAAG;AAER,iBAAW,OAAO,GAAG;AACpB,YAAI,CAAC,KAAK,SAAS,GAAG,EAAG,MAAK,KAAK,GAAG;AAAA,MACvC;AAEA,iBAAW,OAAO,OAAO,sBAAsB,CAAC,GAAG;AAClD,YAAI,CAAC,KAAK,SAAS,GAAG,EAAG,MAAK,KAAK,GAAG;AAAA,MACvC;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AACD;AAMO,SAAS,gBAAgB,OAAO;AACtC,SAAO,IAAI,MAAM,EAAE,MAAM,GAAG,oBAAoB;AACjD;AAMA,SAAS,4BAA4B,eAAe;AAvPpD;AAwPC,WAAO,mBAAc,QAAd,mBAAmB,MAAK;AAChC;AAYO,SAAS,KAAK,OAAO,KAAK,OAAOC,WAAU;AArQlD;AAsQC,MAAI,aAAa,QAAQ,wBAAwB;AACjD,MAAI,QAAQ,CAAC,qBAAqB,QAAQ,oBAAoB;AAC9D,MAAI,YAAY,QAAQ,uBAAuB;AAC/C,MAAI,QAAQ,QAAQ,2BAA2B;AAC/C,MAAI,eAAe;AACnB,MAAI;AAEJ,MAAI,UAAU;AACb,KAAC,YAAY,YAAY,IAAI,sBAAsB;AAAA;AAAA,MAAwB,MAAM,GAAG;AAAA,KAAE;AAAA,EACvF,OAAO;AACN;AAAA,IAA+B,MAAM,GAAG;AAAA,EACzC;AAIA,MAAI,iBAAiB,gBAAgB,SAAS,gBAAgB;AAE9D,MAAI,SACF,eACC,oBAAe,OAAO,GAAG,MAAzB,mBAA4B,SAC3B,kBAAkB,OAAO,UAAU,CAAC,MAAO,MAAM,GAAG,IAAI,QAC3D;AAED,MAAI;AAAA;AAAA,IAAmCA;AAAA;AACvC,MAAI,iBAAiB;AACrB,MAAI,gBAAgB;AAEpB,MAAI,eAAe,MAAM;AACxB,oBAAgB;AAChB,QAAI,gBAAgB;AACnB,uBAAiB;AACjB,UAAI,MAAM;AACT,yBAAiB;AAAA;AAAA,UAAgCA;AAAA,QAAS;AAAA,MAC3D,OAAO;AACN;AAAA,QAAmCA;AAAA,MACpC;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AAEA,MAAI,eAAe,UAAaA,cAAa,QAAW;AACvD,QAAI,UAAU,OAAO;AACpB,MAAE,oBAAoB,GAAG;AAAA,IAC1B;AAEA,iBAAa,aAAa;AAC1B,QAAI,OAAQ,QAAO,UAAU;AAAA,EAC9B;AAGA,MAAI;AACJ,MAAI,OAAO;AACV,aAAS,MAAM;AACd,UAAI;AAAA;AAAA,QAA0B,MAAM,GAAG;AAAA;AACvC,UAAI,UAAU,OAAW,QAAO,aAAa;AAC7C,uBAAiB;AACjB,sBAAgB;AAChB,aAAO;AAAA,IACR;AAAA,EACD,OAAO;AAGN,QAAI,kBAAkB,YAAY,UAAU;AAAA,MAC3C;AAAA;AAAA,QAAwB,MAAM,GAAG;AAAA;AAAA,IAClC;AACA,mBAAe,KAAK;AACpB,aAAS,MAAM;AACd,UAAI,QAAQ,IAAI,cAAc;AAC9B,UAAI,UAAU,OAAW;AAAA,MAAmC;AAC5D,aAAO,UAAU,SAAY,iBAAiB;AAAA,IAC/C;AAAA,EACD;AAGA,OAAK,QAAQ,sBAAsB,GAAG;AACrC,WAAO;AAAA,EACR;AAIA,MAAI,QAAQ;AACX,QAAI,gBAAgB,MAAM;AAC1B,WAAO,SAA6B,OAA8B,UAAU;AAC3E,UAAI,UAAU,SAAS,GAAG;AAKzB,YAAI,CAAC,SAAS,CAAC,YAAY,iBAAiB,cAAc;AACjC,UAAC,OAAQ,WAAW,OAAO,IAAI,KAAK;AAAA,QAC7D;AACA,eAAO;AAAA,MACR,OAAO;AACN,eAAO,OAAO;AAAA,MACf;AAAA,IACD;AAAA,EACD;AAKA,MAAI,aAAa;AACjB,MAAI,iBAAiB;AAIrB,MAAI,sBAAsB,eAAe,UAAU;AACnD,MAAI,gBAAgB,QAAQ,MAAM;AACjC,QAAI,eAAe,OAAO;AAC1B,QAAI,cAAc,IAAI,mBAAmB;AAEzC,QAAI,YAAY;AACf,mBAAa;AACb,uBAAiB;AACjB,aAAO;AAAA,IACR;AAEA,qBAAiB;AACjB,WAAQ,oBAAoB,IAAI;AAAA,EACjC,CAAC;AAGD,MAAI,UAAU;AACb,QAAI,aAAa;AAAA,EAClB;AAEA,MAAI,CAAC,UAAW,eAAc,SAAS;AAEvC,SAAO,SAA6B,OAA8B,UAAU;AAG3E,QAAI,qBAAqB,MAAM;AAI9B,mBAAa;AAEb,aAAO;AACP,UAAI,mBAAmB;AAAA,IACxB;AAEA,QAAI,UAAU,SAAS,GAAG;AACzB,YAAM,YAAY,WAAW,IAAI,aAAa,IAAI,SAAS,WAAW,MAAM,KAAK,IAAI;AAErF,UAAI,CAAC,cAAc,OAAO,SAAS,GAAG;AACrC,qBAAa;AACb,YAAI,qBAAqB,SAAS;AAGlC,YAAI,iBAAiB,mBAAmB,QAAW;AAClD,2BAAiB;AAAA,QAClB;AAEA,YAAI,4BAA4B,aAAa,GAAG;AAC/C,iBAAO;AAAA,QACR;AAEA,gBAAQ,MAAM,IAAI,aAAa,CAAC;AAAA,MACjC;AAEA,aAAO;AAAA,IACR;AAEA,QAAI,4BAA4B,aAAa,GAAG;AAC/C,aAAO,cAAc;AAAA,IACtB;AAEA,WAAO,IAAI,aAAa;AAAA,EACzB;AACD;;;ACrZA,SAAS,cAAcC,WAAU,IAAI;AACpC,MAAI,kBAAkB;AACtB,MAAI,oBAAoB;AACxB,MAAI,eAAe;AAEnB,oBAAkBA,SAAQ;AAC1B,sBAAoBA,SAAQ;AAC5B,wBAAsBA,UAAS,GAAG;AAElC,MAAI;AACH,OAAG;AAAA,EACJ,UAAE;AACD,sBAAkB,eAAe;AACjC,wBAAoB,iBAAiB;AACrC,0BAAsB,YAAY;AAAA,EACnC;AACD;AAWO,SAAS,SAAS,MAAM,OAAO,aAAa;AAClD,MAAI,SAAS;AAGb,MAAI;AAEJ,QAAM,MAAM;AACX,QAAIA;AAAA;AAAA,MAAkC;AAAA;AACtC,QAAI,eAAe;AACnB,QAAI,uBAAuB;AAG3B,IAAAA,UAAS,KAAK,CAAyB,UAAU;AAChD,UAAI,UAAU,MAAM;AACpB,UAAI,SAAS,MAAM;AAInB,UAAK,CAAC,WAAW,CAAC,UAAW,sBAAsB;AAClD,cAAM;AAAA,MACP;AAEA,UAAIC,SAAQ,MAAM;AACjB,qBAAa,eAAe;AAE5B,sBAAcD,WAAU,MAAM;AAC7B,iCAAuB;AACvB,4BAAkB,OAAO,MAAM,YAAY,MAAM,CAAC;AAClD,kCAAwB;AAAA,QACzB,CAAC;AAAA,MACF;AAEA,yCAAU,OAAOC;AAEjB,UAAI,iBAAiB;AACpB,uBAAe,eAAe;AAAA,MAC/B,WAAW,WAAW;AACrB,yBAAiB,YAAY;AAC7B,aAAK;AACL,yBAAiB,aAAa,CAAC;AAAA,MAChC;AAEA,UAAI,QAAQ;AAEX,yBAAiB,MAAM;AACtB,wBAAcD,WAAU,MAAM;AAC7B,mCAAuB;AAEvB,gBAAI;AACH,gCAAkB,OAAO,MAAM;AAC9B;AAAA,kBACC;AAAA,kBACA,MAAM;AAAA,kBACN,MAAMC;AAAA,gBACP;AAAA,cACD,CAAC;AAAA,YACF,SAASC,QAAO;AACf,2BAAaA,QAAOF,WAAU,MAAMA,UAAS,GAAG;AAAA,YACjD;AAEA,oCAAwB;AACxB,mCAAuB;AAAA,UACxB,CAAC;AAAA,QACF,CAAC;AAAA,MACF;AAAA,IACD;AAEA,QAAI,WAAW;AACd,mBAAa;AAAA,IACd;AAEA,sBAAkB,OAAO,MAAM,YAAY,MAAM,CAAC;AAClD,4BAAwB;AAAA,EACzB,GAAG,qBAAqB,eAAe;AAEvC,MAAI,WAAW;AACd,aAAS;AAAA,EACV;AACD;;;ACvHO,SAAS,mBAAmB,YAAY,QAAQ;AACtD,gBAAc,MAAM;AACnB,UAAM,OAAO,oBAAI,IAAI;AACrB,UAAM,cAAc,WAAW;AAC/B,UAAM,QAAQ,SAAS,WAAW,IAC/B,cACA,eAAe,OACd,CAAC,IACD,MAAM,KAAK,WAAW;AAC1B,UAAM,SAAS,MAAM;AACrB,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,YAAM,MAAM,OAAO,MAAM,CAAC,GAAG,CAAC;AAC9B,UAAI,KAAK,IAAI,GAAG,GAAG;AAClB,cAAM,IAAI,OAAO,KAAK,IAAI,GAAG,CAAC;AAC9B,cAAM,IAAI,OAAO,CAAC;AAGlB,YAAI,IAAI,OAAO,GAAG;AAClB,YAAI,EAAE,WAAW,UAAU,EAAG,KAAI;AAElC,QAAE,mBAAmB,GAAG,GAAG,CAAC;AAAA,MAC7B;AACA,WAAK,IAAI,KAAK,CAAC;AAAA,IAChB;AAAA,EACD,CAAC;AACF;AASO,SAAS,iBAAiB,SAAS,YAAY,cAAc,MAAM,QAAQ;AA/ClF;AAgDC,MAAI,SAAS;AAEb,MAAI,YAAW,2DAAiC;AAEhD,gBAAc,MAAM;AACnB,QAAI,OAAQ;AAEZ,QAAI,CAAC,QAAQ,YAAY,IAAI,sBAAsB,UAAU;AAE7D,QAAI,aAAc;AAElB,QAAI,WAAW,aAAa;AAE5B,QAAI,MAAM;AAKV,QAAIG,UAAS,cAAc,MAAM;AAChC,UAAI,IAAK;AAGT,aAAO,QAAQ;AAAA,IAChB,CAAC;AAED,UAAM;AAEN,QAAIA,QAAO,SAAS,MAAM;AACzB,UAAI,WAAW,GAAG,QAAQ,IAAI,IAAI,IAAI,MAAM;AAC5C,MAAE,8BAA8B,SAAS,QAAQ;AAEjD,eAAS;AAAA,IACV;AAAA,EACD,CAAC;AACF;;;ACrEA,IAAI;AAEJ,IAAI,OAAO,gBAAgB,YAAY;AACtC,kBAAgB,cAAc,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IA2BzC,YAAY,iBAAiB,SAAS,gBAAgB;AACrD,YAAM;AA1BP;AAAA;AAEA;AAAA;AAEA;AAAA;AAEA;AAAA,kCAAO;AAEP;AAAA,iCAAM,CAAC;AAEP;AAAA,iCAAM;AAEN;AAAA,mCAAQ,CAAC;AAET;AAAA,iCAAM,CAAC;AAEP;AAAA,mCAAQ,oBAAI,IAAI;AAEhB;AAAA;AASC,WAAK,SAAS;AACd,WAAK,MAAM;AACX,UAAI,gBAAgB;AACnB,aAAK,aAAa,EAAE,MAAM,OAAO,CAAC;AAAA,MACnC;AAAA,IACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,iBAAiB,MAAM,UAAU,SAAS;AAIzC,WAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,CAAC;AACpC,WAAK,IAAI,IAAI,EAAE,KAAK,QAAQ;AAC5B,UAAI,KAAK,KAAK;AACb,cAAM,QAAQ,KAAK,IAAI,IAAI,MAAM,QAAQ;AACzC,aAAK,MAAM,IAAI,UAAU,KAAK;AAAA,MAC/B;AACA,YAAM,iBAAiB,MAAM,UAAU,OAAO;AAAA,IAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,oBAAoB,MAAM,UAAU,SAAS;AAC5C,YAAM,oBAAoB,MAAM,UAAU,OAAO;AACjD,UAAI,KAAK,KAAK;AACb,cAAM,QAAQ,KAAK,MAAM,IAAI,QAAQ;AACrC,YAAI,OAAO;AACV,gBAAM;AACN,eAAK,MAAM,OAAO,QAAQ;AAAA,QAC3B;AAAA,MACD;AAAA,IACD;AAAA,IAEA,MAAM,oBAAoB;AACzB,WAAK,OAAO;AACZ,UAAI,CAAC,KAAK,KAAK;AAOd,YAAS,cAAT,SAAqB,MAAM;AAI1B,iBAAO,CAAC,WAAW;AAClB,kBAAMC,QAAO,SAAS,cAAc,MAAM;AAC1C,gBAAI,SAAS,UAAW,CAAAA,MAAK,OAAO;AAEpC,mBAAO,QAAQA,KAAI;AAAA,UACpB;AAAA,QACD;AAfA,cAAM,QAAQ,QAAQ;AACtB,YAAI,CAAC,KAAK,QAAQ,KAAK,KAAK;AAC3B;AAAA,QACD;AAcA,cAAM,UAAU,CAAC;AACjB,cAAM,iBAAiB,0BAA0B,IAAI;AACrD,mBAAW,QAAQ,KAAK,KAAK;AAC5B,cAAI,QAAQ,gBAAgB;AAC3B,gBAAI,SAAS,aAAa,CAAC,KAAK,IAAI,UAAU;AAC7C,mBAAK,IAAI,WAAW,YAAY,IAAI;AACpC,sBAAQ,UAAU;AAAA,YACnB,OAAO;AACN,sBAAQ,IAAI,IAAI,YAAY,IAAI;AAAA,YACjC;AAAA,UACD;AAAA,QACD;AACA,mBAAW,aAAa,KAAK,YAAY;AAExC,gBAAM,OAAO,KAAK,MAAM,UAAU,IAAI;AACtC,cAAI,EAAE,QAAQ,KAAK,MAAM;AACxB,iBAAK,IAAI,IAAI,IAAI,yBAAyB,MAAM,UAAU,OAAO,KAAK,OAAO,QAAQ;AAAA,UACtF;AAAA,QACD;AAEA,mBAAW,OAAO,KAAK,OAAO;AAE7B,cAAI,EAAE,OAAO,KAAK,QAAQ,KAAK,GAAG,MAAM,QAAW;AAElD,iBAAK,IAAI,GAAG,IAAI,KAAK,GAAG;AAExB,mBAAO,KAAK,GAAG;AAAA,UAChB;AAAA,QACD;AACA,aAAK,MAAM,qBAAqB;AAAA,UAC/B,WAAW,KAAK;AAAA,UAChB,QAAQ,KAAK,cAAc;AAAA,UAC3B,OAAO;AAAA,YACN,GAAG,KAAK;AAAA,YACR;AAAA,YACA,QAAQ;AAAA,UACT;AAAA,QACD,CAAC;AAGD,aAAK,OAAO,YAAY,MAAM;AAC7B,wBAAc,MAAM;AApJzB;AAqJM,iBAAK,MAAM;AACX,uBAAW,OAAO,YAAY,KAAK,GAAG,GAAG;AACxC,kBAAI,GAAC,UAAK,MAAM,GAAG,MAAd,mBAAiB,SAAS;AAC/B,mBAAK,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG;AAC5B,oBAAM,kBAAkB;AAAA,gBACvB;AAAA,gBACA,KAAK,IAAI,GAAG;AAAA,gBACZ,KAAK;AAAA,gBACL;AAAA,cACD;AACA,kBAAI,mBAAmB,MAAM;AAC5B,qBAAK,gBAAgB,KAAK,MAAM,GAAG,EAAE,aAAa,GAAG;AAAA,cACtD,OAAO;AACN,qBAAK,aAAa,KAAK,MAAM,GAAG,EAAE,aAAa,KAAK,eAAe;AAAA,cACpE;AAAA,YACD;AACA,iBAAK,MAAM;AAAA,UACZ,CAAC;AAAA,QACF,CAAC;AAED,mBAAW,QAAQ,KAAK,KAAK;AAC5B,qBAAW,YAAY,KAAK,IAAI,IAAI,GAAG;AACtC,kBAAM,QAAQ,KAAK,IAAI,IAAI,MAAM,QAAQ;AACzC,iBAAK,MAAM,IAAI,UAAU,KAAK;AAAA,UAC/B;AAAA,QACD;AACA,aAAK,MAAM,CAAC;AAAA,MACb;AAAA,IACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,yBAAyBC,OAAM,WAAW,UAAU;AA3LtD;AA4LG,UAAI,KAAK,IAAK;AACd,MAAAA,QAAO,KAAK,MAAMA,KAAI;AACtB,WAAK,IAAIA,KAAI,IAAI,yBAAyBA,OAAM,UAAU,KAAK,OAAO,QAAQ;AAC9E,iBAAK,QAAL,mBAAU,KAAK,EAAE,CAACA,KAAI,GAAG,KAAK,IAAIA,KAAI,EAAE;AAAA,IACzC;AAAA,IAEA,uBAAuB;AACtB,WAAK,OAAO;AAEZ,cAAQ,QAAQ,EAAE,KAAK,MAAM;AAC5B,YAAI,CAAC,KAAK,QAAQ,KAAK,KAAK;AAC3B,eAAK,IAAI,SAAS;AAClB,eAAK,KAAK;AACV,eAAK,MAAM;AAAA,QACZ;AAAA,MACD,CAAC;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,MAAM,gBAAgB;AACrB,aACC,YAAY,KAAK,KAAK,EAAE;AAAA,QACvB,CAAC,QACA,KAAK,MAAM,GAAG,EAAE,cAAc,kBAC7B,CAAC,KAAK,MAAM,GAAG,EAAE,aAAa,IAAI,YAAY,MAAM;AAAA,MACvD,KAAK;AAAA,IAEP;AAAA,EACD;AACD;AAQA,SAAS,yBAAyBC,OAAM,OAAO,kBAAkB,WAAW;AAnO5E;AAoOC,QAAM,QAAO,sBAAiBA,KAAI,MAArB,mBAAwB;AACrC,UAAQ,SAAS,aAAa,OAAO,UAAU,YAAY,SAAS,OAAO;AAC3E,MAAI,CAAC,aAAa,CAAC,iBAAiBA,KAAI,GAAG;AAC1C,WAAO;AAAA,EACR,WAAW,cAAc,eAAe;AACvC,YAAQ,MAAM;AAAA,MACb,KAAK;AAAA,MACL,KAAK;AACJ,eAAO,SAAS,OAAO,OAAO,KAAK,UAAU,KAAK;AAAA,MACnD,KAAK;AACJ,eAAO,QAAQ,KAAK;AAAA,MACrB,KAAK;AACJ,eAAO,SAAS,OAAO,OAAO;AAAA,MAC/B;AACC,eAAO;AAAA,IACT;AAAA,EACD,OAAO;AACN,YAAQ,MAAM;AAAA,MACb,KAAK;AAAA,MACL,KAAK;AACJ,eAAO,SAAS,KAAK,MAAM,KAAK;AAAA,MACjC,KAAK;AACJ,eAAO;AAAA;AAAA,MACR,KAAK;AACJ,eAAO,SAAS,OAAO,CAAC,QAAQ;AAAA,MACjC;AACC,eAAO;AAAA,IACT;AAAA,EACD;AACD;AAKA,SAAS,0BAA0BC,UAAS;AAE3C,QAAM,SAAS,CAAC;AAChB,EAAAA,SAAQ,WAAW,QAAQ,CAAC,SAAS;AACpC;AAAA;AAAA,MAAoC,KAAM,QAAQ;AAAA,IAAS,IAAI;AAAA,EAChE,CAAC;AACD,SAAO;AACR;AAaO,SAAS,sBACf,WACA,kBACA,OACA,SACA,gBACA,QACC;AACD,MAAI,QAAQ,cAAc,cAAc;AAAA,IACvC,cAAc;AACb,YAAM,WAAW,OAAO,cAAc;AACtC,WAAK,QAAQ;AAAA,IACd;AAAA,IACA,WAAW,qBAAqB;AAC/B,aAAO,YAAY,gBAAgB,EAAE;AAAA,QAAI,CAAC,SACxC,iBAAiB,GAAG,EAAE,aAAa,KAAK,YAAY;AAAA,MACtD;AAAA,IACD;AAAA,EACD;AACA,cAAY,gBAAgB,EAAE,QAAQ,CAACD,UAAS;AAC/C,oBAAgB,MAAM,WAAWA,OAAM;AAAA,MACtC,MAAM;AACL,eAAO,KAAK,OAAOA,SAAQ,KAAK,MAAM,KAAK,IAAIA,KAAI,IAAI,KAAK,IAAIA,KAAI;AAAA,MACrE;AAAA,MACA,IAAI,OAAO;AAlTd;AAmTI,gBAAQ,yBAAyBA,OAAM,OAAO,gBAAgB;AAC9D,aAAK,IAAIA,KAAI,IAAI;AACjB,YAAIE,aAAY,KAAK;AAErB,YAAIA,YAAW;AAEd,cAAI,UAAS,oBAAeA,YAAWF,KAAI,MAA9B,mBAAiC;AAE9C,cAAI,QAAQ;AACX,YAAAE,WAAUF,KAAI,IAAI;AAAA,UACnB,OAAO;AACN,YAAAE,WAAU,KAAK,EAAE,CAACF,KAAI,GAAG,MAAM,CAAC;AAAA,UACjC;AAAA,QACD;AAAA,MACD;AAAA,IACD,CAAC;AAAA,EACF,CAAC;AACD,UAAQ,QAAQ,CAAC,aAAa;AAC7B,oBAAgB,MAAM,WAAW,UAAU;AAAA,MAC1C,MAAM;AAtUT;AAuUI,gBAAO,UAAK,QAAL,mBAAW;AAAA,MACnB;AAAA,IACD,CAAC;AAAA,EACF,CAAC;AACD,MAAI,QAAQ;AAEX,YAAQ,OAAO,KAAK;AAAA,EACrB;AACA,YAAU;AAAA,EAA6B;AACvC,SAAO;AACR;;;ACxUO,SAAS,sBAAsB,WAAW,SAAS;AACzD,UAAQ,MAAM;AACb,QAAI;AACH,UAAI,YAAY;AAChB,YAAM,cAAc,CAAC;AAErB,iBAAW,OAAO,SAAS;AAC1B,YAAI,OAAO,OAAO,QAAQ,YAAY,gBAAgB,KAAK;AAC1D,sBAAY,KAAK,SAAS,KAAK,IAAI,CAAC;AACpC,sBAAY;AAAA,QACb,OAAO;AACN,sBAAY,KAAK,GAAG;AAAA,QACrB;AAAA,MACD;AAEA,UAAI,WAAW;AACd,QAAE,kBAAkB,MAAM;AAG1B,gBAAQ,IAAI,gBAAgB,eAAe,GAAG,WAAW;AAAA,MAC1D;AAAA,IACD,QAAQ;AAAA,IAAC;AAAA,EACV,CAAC;AAED,SAAO;AACR;;;ACLO,SAAS,sBAAsB;AACrC,SAAO,OAAO,cAAc;AAC7B;AAgEO,SAAS,WAAWG,SAAQ;AAAA;AAAA,EAA6B;AAAA,GAAO;AACtE,SAAO,CAACC,aAAY;AACnB,UAAM,EAAE,QAAAC,SAAQ,QAAQ,IAAI,QAAQ,MAAMF,QAAOC,UAAS,GAAG,CAAC,KAAK,CAAC,CAAC;AAErE,QAAIC,SAAQ;AACX,UAAI,MAAM;AACV,oBAAc,MAAM;AACnB,cAAM,MAAM,GAAG;AACf,YAAI,IAAK,CAAAA,QAAO,GAAG;AAAA,MACpB,CAAC;AACD,YAAM;AAAA,IACP;AAEA,QAAI,SAAS;AACZ,eAAS,OAAO;AAAA,IACjB;AAAA,EACD;AACD;", "names": ["hash", "element", "comment", "effect", "source", "component", "component", "prop", "_a", "component", "update", "state", "effect", "fn", "effect", "element", "state", "i", "fallback", "_a", "index", "next", "element", "effect", "hash", "next", "html", "node", "snippet", "component", "element", "html", "component", "effect", "element", "effect", "action", "clsx", "hash", "append_styles", "hash", "next", "element", "prop", "next", "clsx", "key", "now", "element", "animation", "to", "transition", "run", "block", "tick", "keyframes", "styles", "t", "update", "event", "get", "set", "hydration_mismatch", "index", "input", "get", "set", "update", "update", "prop", "get", "set", "element", "listeners", "set", "update", "element", "get", "set", "get", "set", "props", "event", "event", "run", "set", "update", "run", "derived", "pending", "get", "get", "fallback", "boundary", "reset", "error", "effect", "slot", "attr", "prop", "element", "component", "action", "element", "update"]}