{"version": 3, "sources": ["../../svelte/src/reactivity/window/index.js"], "sourcesContent": ["import { BROWSER } from 'esm-env';\nimport { on } from '../../events/index.js';\nimport { ReactiveValue } from '../reactive-value.js';\nimport { get } from '../../internal/client/index.js';\nimport { set, source } from '../../internal/client/reactivity/sources.js';\n\n/**\n * `scrollX.current` is a reactive view of `window.scrollX`. On the server it is `undefined`.\n * @since 5.11.0\n */\nexport const scrollX = new ReactiveValue(\n\tBROWSER ? () => window.scrollX : () => undefined,\n\t(update) => on(window, 'scroll', update)\n);\n\n/**\n * `scrollY.current` is a reactive view of `window.scrollY`. On the server it is `undefined`.\n * @since 5.11.0\n */\nexport const scrollY = new ReactiveValue(\n\tBROWSER ? () => window.scrollY : () => undefined,\n\t(update) => on(window, 'scroll', update)\n);\n\n/**\n * `innerWidth.current` is a reactive view of `window.innerWidth`. On the server it is `undefined`.\n * @since 5.11.0\n */\nexport const innerWidth = new ReactiveValue(\n\tBROWSER ? () => window.innerWidth : () => undefined,\n\t(update) => on(window, 'resize', update)\n);\n\n/**\n * `innerHeight.current` is a reactive view of `window.innerHeight`. On the server it is `undefined`.\n * @since 5.11.0\n */\nexport const innerHeight = new ReactiveValue(\n\tBROWSER ? () => window.innerHeight : () => undefined,\n\t(update) => on(window, 'resize', update)\n);\n\n/**\n * `outerWidth.current` is a reactive view of `window.outerWidth`. On the server it is `undefined`.\n * @since 5.11.0\n */\nexport const outerWidth = new ReactiveValue(\n\tBROWSER ? () => window.outerWidth : () => undefined,\n\t(update) => on(window, 'resize', update)\n);\n\n/**\n * `outerHeight.current` is a reactive view of `window.outerHeight`. On the server it is `undefined`.\n * @since 5.11.0\n */\nexport const outerHeight = new ReactiveValue(\n\tBROWSER ? () => window.outerHeight : () => undefined,\n\t(update) => on(window, 'resize', update)\n);\n\n/**\n * `screenLeft.current` is a reactive view of `window.screenLeft`. It is updated inside a `requestAnimationFrame` callback. On the server it is `undefined`.\n * @since 5.11.0\n */\nexport const screenLeft = new ReactiveValue(\n\tBROWSER ? () => window.screenLeft : () => undefined,\n\t(update) => {\n\t\tlet value = window.screenLeft;\n\n\t\tlet frame = requestAnimationFrame(function check() {\n\t\t\tframe = requestAnimationFrame(check);\n\n\t\t\tif (value !== (value = window.screenLeft)) {\n\t\t\t\tupdate();\n\t\t\t}\n\t\t});\n\n\t\treturn () => {\n\t\t\tcancelAnimationFrame(frame);\n\t\t};\n\t}\n);\n\n/**\n * `screenTop.current` is a reactive view of `window.screenTop`. It is updated inside a `requestAnimationFrame` callback. On the server it is `undefined`.\n * @since 5.11.0\n */\nexport const screenTop = new ReactiveValue(\n\tBROWSER ? () => window.screenTop : () => undefined,\n\t(update) => {\n\t\tlet value = window.screenTop;\n\n\t\tlet frame = requestAnimationFrame(function check() {\n\t\t\tframe = requestAnimationFrame(check);\n\n\t\t\tif (value !== (value = window.screenTop)) {\n\t\t\t\tupdate();\n\t\t\t}\n\t\t});\n\n\t\treturn () => {\n\t\t\tcancelAnimationFrame(frame);\n\t\t};\n\t}\n);\n\n/**\n * `online.current` is a reactive view of `navigator.onLine`. On the server it is `undefined`.\n * @since 5.11.0\n */\nexport const online = new ReactiveValue(\n\tBROWSER ? () => navigator.onLine : () => undefined,\n\t(update) => {\n\t\tconst unsub_online = on(window, 'online', update);\n\t\tconst unsub_offline = on(window, 'offline', update);\n\t\treturn () => {\n\t\t\tunsub_online();\n\t\t\tunsub_offline();\n\t\t};\n\t}\n);\n\n/**\n * `devicePixelRatio.current` is a reactive view of `window.devicePixelRatio`. On the server it is `undefined`.\n * Note that behaviour differs between browsers — on Chrome it will respond to the current zoom level,\n * on Firefox and Safari it won't.\n * @type {{ get current(): number | undefined }}\n * @since 5.11.0\n */\nexport const devicePixelRatio = /* @__PURE__ */ new (class DevicePixelRatio {\n\t#dpr = source(BROWSER ? window.devicePixelRatio : undefined);\n\n\t#update() {\n\t\tconst off = on(\n\t\t\twindow.matchMedia(`(resolution: ${window.devicePixelRatio}dppx)`),\n\t\t\t'change',\n\t\t\t() => {\n\t\t\t\tset(this.#dpr, window.devicePixelRatio);\n\n\t\t\t\toff();\n\t\t\t\tthis.#update();\n\t\t\t}\n\t\t);\n\t}\n\n\tconstructor() {\n\t\tif (BROWSER) {\n\t\t\tthis.#update();\n\t\t}\n\t}\n\n\tget current() {\n\t\tget(this.#dpr);\n\t\treturn BROWSER ? window.devicePixelRatio : undefined;\n\t}\n})();\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAUO,IAAM,UAAU,IAAI;AAAA,EAC1B,eAAU,MAAM,OAAO,UAAU,MAAM;AAAA,EACvC,CAAC,WAAW,GAAG,QAAQ,UAAU,MAAM;AACxC;AAMO,IAAM,UAAU,IAAI;AAAA,EAC1B,eAAU,MAAM,OAAO,UAAU,MAAM;AAAA,EACvC,CAAC,WAAW,GAAG,QAAQ,UAAU,MAAM;AACxC;AAMO,IAAM,aAAa,IAAI;AAAA,EAC7B,eAAU,MAAM,OAAO,aAAa,MAAM;AAAA,EAC1C,CAAC,WAAW,GAAG,QAAQ,UAAU,MAAM;AACxC;AAMO,IAAM,cAAc,IAAI;AAAA,EAC9B,eAAU,MAAM,OAAO,cAAc,MAAM;AAAA,EAC3C,CAAC,WAAW,GAAG,QAAQ,UAAU,MAAM;AACxC;AAMO,IAAM,aAAa,IAAI;AAAA,EAC7B,eAAU,MAAM,OAAO,aAAa,MAAM;AAAA,EAC1C,CAAC,WAAW,GAAG,QAAQ,UAAU,MAAM;AACxC;AAMO,IAAM,cAAc,IAAI;AAAA,EAC9B,eAAU,MAAM,OAAO,cAAc,MAAM;AAAA,EAC3C,CAAC,WAAW,GAAG,QAAQ,UAAU,MAAM;AACxC;AAMO,IAAM,aAAa,IAAI;AAAA,EAC7B,eAAU,MAAM,OAAO,aAAa,MAAM;AAAA,EAC1C,CAAC,WAAW;AACX,QAAI,QAAQ,OAAO;AAEnB,QAAI,QAAQ,sBAAsB,SAAS,QAAQ;AAClD,cAAQ,sBAAsB,KAAK;AAEnC,UAAI,WAAW,QAAQ,OAAO,aAAa;AAC1C,eAAO;AAAA,MACR;AAAA,IACD,CAAC;AAED,WAAO,MAAM;AACZ,2BAAqB,KAAK;AAAA,IAC3B;AAAA,EACD;AACD;AAMO,IAAM,YAAY,IAAI;AAAA,EAC5B,eAAU,MAAM,OAAO,YAAY,MAAM;AAAA,EACzC,CAAC,WAAW;AACX,QAAI,QAAQ,OAAO;AAEnB,QAAI,QAAQ,sBAAsB,SAAS,QAAQ;AAClD,cAAQ,sBAAsB,KAAK;AAEnC,UAAI,WAAW,QAAQ,OAAO,YAAY;AACzC,eAAO;AAAA,MACR;AAAA,IACD,CAAC;AAED,WAAO,MAAM;AACZ,2BAAqB,KAAK;AAAA,IAC3B;AAAA,EACD;AACD;AAMO,IAAM,SAAS,IAAI;AAAA,EACzB,eAAU,MAAM,UAAU,SAAS,MAAM;AAAA,EACzC,CAAC,WAAW;AACX,UAAM,eAAe,GAAG,QAAQ,UAAU,MAAM;AAChD,UAAM,gBAAgB,GAAG,QAAQ,WAAW,MAAM;AAClD,WAAO,MAAM;AACZ,mBAAa;AACb,oBAAc;AAAA,IACf;AAAA,EACD;AACD;AAxHA;AAiIO,IAAM,mBAAmC,KAAK,WAAuB;AAAA,EAgB3E,cAAc;AAhBsC;AACpD,6BAAO,OAAO,eAAU,OAAO,mBAAmB,MAAS;AAgB1D,QAAI,cAAS;AACZ,4BAAK,wCAAL;AAAA,IACD;AAAA,EACD;AAAA,EAEA,IAAI,UAAU;AACb,QAAI,mBAAK,KAAI;AACb,WAAO,eAAU,OAAO,mBAAmB;AAAA,EAC5C;AACD,GAzBC,sBADoD,6CAGpD,YAAO,WAAG;AACT,QAAM,MAAM;AAAA,IACX,OAAO,WAAW,gBAAgB,OAAO,gBAAgB,OAAO;AAAA,IAChE;AAAA,IACA,MAAM;AACL,UAAI,mBAAK,OAAM,OAAO,gBAAgB;AAEtC,UAAI;AACJ,4BAAK,wCAAL;AAAA,IACD;AAAA,EACD;AACD,GAdoD,IA0BlD;", "names": []}