// @ts-nocheck
import { error } from '@sveltejs/kit'
import { ProductsAPI } from '$lib/api/products'
import { RegionsAPI } from '$lib/api/regions'
import type { PageLoad } from './$types'

export const load = async ({ params }: Parameters<PageLoad>[0]) => {
  const { countryCode, handle } = params

  try {
    // Get region by country code
    const region = await RegionsAPI.getRegionByCountry(countryCode)
    
    if (!region) {
      throw error(404, 'Region not found')
    }

    // Get product by handle
    const product = await ProductsAPI.getProductByHandle(handle, region.id)
    
    if (!product) {
      throw error(404, 'Product not found')
    }

    return {
      product,
      region,
      countryCode
    }
  } catch (err) {
    console.error('Error loading product page:', err)
    
    if (err && typeof err === 'object' && 'status' in err) {
      throw err // Re-throw SvelteKit errors
    }
    
    throw error(500, 'Failed to load product')
  }
}
